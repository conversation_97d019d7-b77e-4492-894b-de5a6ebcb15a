<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DecA(I)de SAT Prep</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Lora:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Chosen Palette: Professional Ed-Tech Blue */
        /* Application Structure Plan: A multi-view SPA with clear separation between learning and assessment. 1) Dashboard: A professional landing page. 2) Study Guide: The complete interactive 27-rule textbook. 3) Practice Test: An adaptive, multi-module assessment that mimics the real digital SAT experience. 4) Review Mode: A post-test screen to review answers and explanations. This structure provides a comprehensive, unicorn-level user journey. */
        /* Visualization & Content Choices: The core visualization is the final score report, using Chart.js for a dynamic bar chart showing module performance. The main interactive feature is the AI-powered question generator, which creates realistic SAT questions by being prompted with specific, high-value concepts from the study guide (e.g., 'Faulty Comparisons', 'Analyzing Author's Tone'). This ensures practice is always relevant and targeted. */
        /* CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. */
        :root {
            --bg-main: #f8f9fa;
            --bg-sidebar: #ffffff;
            --text-primary: #212529;
            --text-secondary: #495057;
            --primary-accent: #0d6efd;
            --primary-accent-hover: #0b5ed7;
            --secondary-accent: #ffc107;
            --border-color: #dee2e6;
            --success-color: #198754;
            --danger-color: #dc3545;
        }
        body { font-family: 'Inter', sans-serif; background-color: var(--bg-main); color: var(--text-primary); }
        h1, h2, h3, h4 { font-family: 'Lora', serif; }
        .sidebar-link.active { background-color: #e7f1ff; color: var(--primary-accent); font-weight: 600; }
        .sat-test-option { border: 2px solid var(--border-color); transition: all 0.2s ease-in-out; }
        .sat-test-option:hover { border-color: var(--primary-accent); background-color: #f8f9fa; }
        .sat-test-option.selected { border-color: var(--primary-accent); background-color: #e7f1ff; box-shadow: 0 0 0 2px var(--primary-accent); }
        .sat-test-option.correct { border-color: var(--success-color); background-color: #d1e7dd; }
        .sat-test-option.incorrect { border-color: var(--danger-color); background-color: #f8d7da; }
        .progress-bar-fill { transition: width 0.5s ease-in-out; background-color: var(--secondary-accent); }
        .loader { width: 52px; height: 52px; border: 5px solid rgba(255, 255, 255, 0.2); border-bottom-color: var(--secondary-accent); border-radius: 50%; display: inline-block; box-sizing: border-box; animation: rotation 1s linear infinite; }
        @keyframes rotation { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .quiz-option { border: 2px solid var(--border-color); transition: all 0.2s ease-in-out; }
        .quiz-option:hover { border-color: var(--primary-accent); background-color: #f8f9fa; }
        .quiz-option.correct { border-color: var(--success-color); background-color: #d1e7dd; }
        .quiz-option.incorrect { border-color: var(--danger-color); background-color: #f8d7da; }
        .view { transition: opacity 0.3s ease-in-out; }
    </style>
</head>
<body class="antialiased">

    <div id="app" class="min-h-screen">
        
        <div id="loading-screen" class="fixed inset-0 bg-gray-900 bg-opacity-95 flex-col z-50 flex justify-center items-center text-white text-center p-4 transition-opacity duration-500">
            <div class="loader mb-6"></div>
            <h2 class="text-2xl font-bold mb-2 text-gray-100">Initializing DecA(I)de...</h2>
            <p id="loading-text" class="text-lg text-gray-300 w-full max-w-md"></p>
        </div>

        <div id="app-content" class="hidden opacity-0 transition-opacity duration-500">
            
            <div id="view-dashboard" class="view">
                <header class="bg-white shadow-sm border-b border-[var(--border-color)]">
                    <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
                        <h1 class="text-2xl font-bold text-[var(--primary-accent)]">DecA(I)de SAT Prep</h1>
                        <div>
                            <button class="nav-link text-gray-600 hover:text-[var(--primary-accent)] font-medium" data-view="dashboard">Home</button>
                            <button class="nav-link text-gray-600 hover:text-[var(--primary-accent)] font-medium ml-6" data-view="study-guide">Study Guide</button>
                            <button class="nav-link text-gray-600 hover:text-[var(--primary-accent)] font-medium ml-6" data-view="practice-test-intro">Practice Test</button>
                        </div>
                    </nav>
                </header>
                <main class="container mx-auto px-6 py-16">
                    <div class="text-center">
                        <h2 class="text-6xl font-extrabold text-gray-800 mb-4">A Decade of Learning, in an Instant.</h2>
                        <p class="text-xl text-gray-500 mb-10 max-w-3xl mx-auto">Harness the power of AI-driven practice and a comprehensive, interactive guide to achieve your perfect score.</p>
                    </div>
                    <div id="dashboard-content" class="grid md:grid-cols-2 gap-8 mt-12 max-w-5xl mx-auto">
                        <div class="bg-white p-8 rounded-lg shadow-lg hover:shadow-2xl transition-shadow duration-300 flex flex-col">
                            <h3 class="text-2xl font-bold text-[var(--primary-accent)] mb-4">AI Practice Test</h3>
                            <p class="text-[var(--text-secondary)] mb-6 flex-grow">Take a full-length, adaptive Reading & Writing test. Our AI generates unique questions that mirror the official Digital SAT, adjusting the difficulty based on your performance.</p>
                            <button class="start-btn mt-auto text-white font-bold py-3 px-6 rounded-lg transition-transform transform hover:scale-105 w-full" style="background-color: var(--primary-accent);" onmouseover="this.style.backgroundColor='var(--primary-accent-hover)';" onmouseout="this.style.backgroundColor='var(--primary-accent)';" data-view="practice-test-intro">Start Adaptive Test</button>
                        </div>
                        <div class="bg-white p-8 rounded-lg shadow-lg hover:shadow-2xl transition-shadow duration-300 flex flex-col">
                            <h3 class="text-2xl font-bold text-[var(--primary-accent)] mb-4">Interactive Study Guide</h3>
                            <p class="text-[var(--text-secondary)] mb-6 flex-grow">Dive into our complete 800-level guide. Master every grammar rule and reading strategy with interactive examples and clear, concise explanations.</p>
                            <button class="start-btn mt-auto text-[var(--primary-accent)] font-bold py-3 px-6 rounded-lg border-2 border-[var(--border-color)] hover:bg-gray-100 transition-transform transform hover:scale-105 w-full" data-view="study-guide">Open Study Guide</button>
                        </div>
                    </div>
                </main>
            </div>

            <div id="view-study-guide" class="hidden view">
                 <div class="flex h-screen">
                    <aside id="sidebar" class="w-72 h-full bg-[var(--bg-sidebar)] p-4 overflow-y-auto border-r border-[var(--border-color)] transition-transform -translate-x-full md:translate-x-0 fixed md:relative z-30">
                        <div class="flex justify-between items-center mb-4">
                            <h2 class="text-xl font-bold text-[var(--primary-accent)]">Study Guide</h2>
                            <button class="home-btn p-2 rounded-md hover:bg-gray-200" title="Return to Home">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path><polyline points="9 22 9 12 15 12 15 22"></polyline></svg>
                            </button>
                        </div>
                        <nav id="navigation-menu"></nav>
                    </aside>
                    <main class="flex-1 p-4 md:p-8 overflow-y-auto">
                        <button id="menu-toggle" class="md:hidden fixed top-4 left-4 z-40 p-2 bg-white rounded-md shadow">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" /></svg>
                        </button>
                        <div id="content-area" class="max-w-4xl mx-auto"></div>
                    </main>
                </div>
            </div>

            <div id="view-practice-test-intro" class="hidden view">
                 <header class="bg-white shadow-sm">
                    <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
                        <h1 class="text-2xl font-bold text-[var(--primary-accent)]">DecA(I)de SAT Prep</h1>
                         <button class="home-btn text-gray-600 hover:text-[var(--primary-accent)] font-medium ml-6">Home</button>
                    </nav>
                </header>
                <main class="container mx-auto px-6 py-16 text-center">
                    <h2 class="text-4xl font-bold text-gray-800 mb-4">Reading & Writing Practice Test</h2>
                    <p class="text-lg text-gray-500 mb-8 max-w-2xl mx-auto">This is a digital adaptive test with two modules. Your performance on Module 1 will determine the difficulty of Module 2. Good luck.</p>
                    <button id="start-test-btn" class="text-white font-bold py-3 px-8 rounded-full transition-transform transform hover:scale-105" style="background-color: var(--primary-accent);" onmouseover="this.style.backgroundColor='var(--primary-accent-hover)';" onmouseout="this.style.backgroundColor='var(--primary-accent)';">Start Module 1</button>
                </main>
            </div>
            
            <div id="view-practice-test" class="hidden view bg-gray-100 min-h-screen p-2 md:p-4">
                <div class="max-w-7xl mx-auto">
                    <header class="flex justify-between items-center py-2 px-2">
                        <div class="text-md md:text-lg font-semibold text-gray-700"><span id="test-section-title"></span></div>
                        <div class="flex items-center space-x-4">
                            <div id="test-timer" class="text-lg font-bold bg-white px-3 py-1 rounded-md shadow-sm">--:--</div>
                        </div>
                    </header>
                    <div class="w-full h-1 bg-gray-300 my-2 rounded-full overflow-hidden">
                         <div id="test-progress-bar-top" class="h-full progress-bar-fill"></div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6 mt-4">
                        <div id="test-passage-container" class="bg-white p-4 md:p-6 rounded-lg shadow-sm overflow-y-auto" style="max-height: 78vh;"></div>
                        <div id="test-question-container" class="bg-white p-4 md:p-6 rounded-lg shadow-sm flex flex-col"></div>
                    </div>

                    <footer class="flex justify-between items-center mt-4 px-2">
                        <div id="question-counter" class="font-semibold text-gray-600"></div>
                        <button id="next-question-btn" class="text-white font-bold py-2 px-8 rounded-md hover:bg-[var(--primary-accent-hover)] disabled:bg-gray-400" style="background-color: var(--primary-accent);">Next</button>
                    </footer>
                </div>
            </div>

            <div id="view-score-report" class="hidden view">
                 <header class="bg-white shadow-sm">
                    <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
                        <h1 class="text-2xl font-bold text-[var(--primary-accent)]">DecA(I)de SAT Prep</h1>
                         <button class="home-btn text-gray-600 hover:text-[var(--primary-accent)] font-medium ml-6">Home</button>
                    </nav>
                </header>
                <main class="container mx-auto px-6 py-12">
                    <div class="bg-white p-8 rounded-xl shadow-lg max-w-4xl mx-auto">
                        <h2 class="text-4xl font-bold text-center text-gray-800 mb-2">Test Complete!</h2>
                        <p class="text-center text-gray-500 mb-8">Here's your performance breakdown.</p>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
                            <div class="text-center">
                                <h3 class="text-lg text-gray-500">Reading & Writing Scaled Score</h3>
                                <p id="scaled-score" class="text-8xl font-bold text-[var(--primary-accent)]">---</p>
                                <div class="mt-4 space-y-2">
                                     <p id="total-correct" class="text-xl font-semibold text-gray-700">--/-- Correct</p>
                                     <p id="module1-correct" class="text-md text-gray-500">Module 1: --/27</p>
                                     <p id="module2-correct" class="text-md text-gray-500">Module 2: --/27</p>
                                </div>
                            </div>
                            <div class="chart-container" style="position: relative; height:300px; max-width:400px; margin: auto;">
                                <canvas id="scoreChart"></canvas>
                            </div>
                        </div>
                        <div class="text-center mt-12 space-x-4">
                            <button id="review-answers-btn" class="text-white font-bold py-3 px-8 rounded-full transition-transform transform hover:scale-105" style="background-color: var(--primary-accent);" onmouseover="this.style.backgroundColor='var(--primary-accent-hover)';" onmouseout="this.style.backgroundColor='var(--primary-accent)';">Review Answers</button>
                            <button class="home-btn text-[var(--primary-accent)] font-bold py-3 px-8 rounded-full border-2 border-[var(--border-color)] hover:bg-gray-100 transition-transform transform hover:scale-105">Return to Dashboard</button>
                        </div>
                    </div>
                </main>
            </div>

            <div id="view-review-test" class="hidden view bg-gray-100 min-h-screen p-2 md:p-4">
                 <div class="max-w-7xl mx-auto">
                    <header class="flex justify-between items-center py-2 px-2">
                        <div class="text-md md:text-lg font-semibold text-gray-700">Reviewing Test</div>
                        <button class="home-btn text-white font-bold py-2 px-6 rounded-md" style="background-color: var(--primary-accent);">End Review</button>
                    </header>
                    <div class="w-full h-1 bg-gray-300 my-2 rounded-full overflow-hidden">
                         <div id="review-progress-bar" class="h-full progress-bar-fill"></div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6 mt-4">
                        <div id="review-passage-container" class="bg-white p-4 md:p-6 rounded-lg shadow-sm overflow-y-auto" style="max-height: 78vh;"></div>
                        <div id="review-question-container" class="bg-white p-4 md:p-6 rounded-lg shadow-sm flex flex-col"></div>
                    </div>
                    <footer class="flex justify-between items-center mt-4 px-2">
                         <button id="prev-review-btn" class="font-bold py-2 px-6 rounded-md border-2 border-[var(--border-color)] hover:bg-gray-100 disabled:opacity-50">Previous</button>
                         <div id="review-question-counter" class="font-semibold text-gray-600"></div>
                         <button id="next-review-btn" class="font-bold py-2 px-8 rounded-md border-2 border-[var(--border-color)] hover:bg-gray-100 disabled:opacity-50">Next</button>
                    </footer>
                </div>
            </div>

        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        const guideData = [
            {
                part: "Part 1: The Foundations of Sentence Structure",
                rules: [
                    { id: 'rule1', title: '1. Sentence vs. Fragment', content: { cheatCode: 'A sentence must have a subject (a "who" or "what") and a main verb (a "did what"). If it\'s missing either, it\'s a fragment.', layman: 'Think of it as the "who did what" test. Every complete thought must name a subject and state what that subject is or does. "The dog barked" passes the test. "The dog barking loudly" fails, because "barking" isn\'t a main verb—it doesn\'t state a complete action. "Because the dog barked" also fails, because it\'s an incomplete thought that leaves you hanging.', nuance: 'The SAT creates fragments in two predictable ways:<br>1. <strong>Dependent Clauses:</strong> A clause that starts with a subordinating word (like `although`, `because`, `since`, `which`, `who`) is always a fragment, even though it has a subject and verb. It cannot stand alone.<br>2. <strong>-ING Phrases:</strong> An `-ing` word (a participle) is a descriptive word, not a main verb. A phrase built around an `-ing` word is always a fragment unless it\'s attached to a complete sentence.', examples: [{ question: "Which of the following is a complete sentence?", options: ["The scientist, who made a major discovery.", "The scientist made a major discovery."], correct: 1, explanation: 'The first option is a fragment because the word "who" creates a dependent clause. The second option is a complete sentence with a subject ("scientist") and a main verb ("made").' }, { question: "Which of the following is a fragment?", options: ["The car drives down the street.", "The car driving down the street."], correct: 1, explanation: 'The second option is a fragment because "driving" is a participle (a descriptive word), not a main verb. The first option is a complete sentence.' }] } },
                    { id: 'rule2', title: '2. Connecting Complete Sentences', content: { cheatCode: 'To connect two complete sentences, you must use one of three things: a period, a semicolon, or a comma + a FANBOYS conjunction.', layman: 'Think of two separate train cars (two complete sentences). To connect them properly, you have three options:<br>1. <strong>Period (.):</strong> Leave a clear space between them. `She left. He stayed.`<br>2. <strong>Semicolon (;):</strong> Use a strong, direct metal coupler. `She left; he stayed.`<br>3. <strong>Comma + FANBOYS (, and/but/so...):</strong> Use a weaker coupler that requires a pin to hold it together. `She left, but he stayed.`', nuance: 'This creates the most powerful strategic shortcut on the test, the <strong>"Period Test."</strong> Because a period, a semicolon, and a comma + `and`/`but` are all grammatically identical ways to separate two sentences, they can\'t all be options if only one answer is correct.<br><strong>Strategy:</strong> If you see answer choices that include `sentence. New sentence`, `sentence; new sentence`, and `sentence, and new sentence`, you can eliminate all of them at once. The correct answer must be something else.', examples: [{ question: "Which sentence correctly connects two complete thoughts?", options: ["The experiment was a success, the results were published.", "The experiment was a success; the results were published."], correct: 1, explanation: 'The first option is a comma splice. A comma alone cannot connect two complete sentences. The second option correctly uses a semicolon.' }] } },
                    { id: 'rule3', title: '3. The Comma Splice', content: { cheatCode: 'A comma by itself is too weak to connect two complete sentences.', layman: 'You can\'t use a single piece of tape to hold two heavy boxes together—it will break in the middle. The comma is that weak piece of tape, and the complete sentences are the heavy boxes. Using only a comma to connect them creates a "comma splice."', nuance: 'The SAT loves comma splices. The most common trap format is: <strong>`...complete sentence, pronoun...`</strong> (e.g., "...the system failed, *it* needed to be redesigned."). When you see this, you know you must fix it in one of four ways:<br>1. Change the comma to a period.<br>2. Change the comma to a semicolon.<br>3. Add a FANBOYS conjunction after the comma.<br>4. Make one clause dependent (subordinate).', examples: [{ question: "Identify the comma splice.", options: ["The artist was a visionary, for she was ahead of her time.", "The artist was a visionary, she was ahead of her time."], correct: 1, explanation: 'The second sentence is a comma splice because it uses only a comma to connect two complete sentences. The first sentence correctly uses a comma followed by a FANBOYS conjunction ("for").' }] } }
                ]
            },
            {
                part: "Part 2: Mastering Punctuation",
                rules: [
                    { id: 'rule4', title: '4. The Semicolon (;)', content: { cheatCode: 'A semicolon is a "super-period" that connects two *closely related* complete sentences.', layman: 'Use a semicolon only where you could also use a period. The semicolon just adds a bit of style, suggesting the two sentences are linked thematically. If you can\'t swap the semicolon for a period, it\'s wrong.', nuance: 'The SAT tests this in two ways:<br>1. <strong>Semicolon vs. Comma:</strong> The most common error is using a semicolon to connect a complete sentence to a fragment. Remember: both sides of a semicolon must be complete sentences.<br>2. <strong>Semicolon with Transitions:</strong> A semicolon is used *before* transition words like `however`, `therefore`, and `consequently`. A comma comes *after* them.', examples: [{ question: "Which sentence uses the semicolon correctly?", options: ["The sun set; revealing the stars.", "The sun set; the stars emerged."], correct: 1, explanation: 'The first option is incorrect because "revealing the stars" is a fragment. The second option is correct because both clauses are complete sentences.' }] } },
                    { id: 'rule5', title: '5. The Colon (:)', content: { cheatCode: 'A colon must follow a complete sentence and introduces a list, explanation, or quotation.', layman: 'A colon acts like a gateway. The rule is that the statement *before* the gateway must be a complete sentence that could end with a period. What comes *after* the gateway can be a list, a clarification, or an example. Think of the colon as meaning "and here\'s what I mean."', nuance: 'The SAT sets traps by placing colons after fragments. The most common trap is using a colon after phrases like "such as" or "including."<br><strong>Strategy:</strong> To check if a colon is correct, cover up everything after it and read what comes before. If it\'s a complete, standalone sentence, the colon is likely correct.', examples: [{ question: "Which sentence uses the colon correctly?", options: ["The recipe included ingredients such as: flour, sugar, and eggs.", "The recipe included three main ingredients: flour, sugar, and eggs."], correct: 1, explanation: 'The first option is incorrect because "The recipe included ingredients such as" is not a complete sentence. The second option is correct because the clause before the colon is a complete sentence.' }] } },
                    { id: 'rule6', title: '6. Dashes & Parentheses', content: { cheatCode: 'Dashes, commas, and parentheses can all set off extra, non-essential information, but you can never mix and match them.', layman: 'Think of these as written "air quotes" or a way to whisper an aside to the reader. The rule is simple: if you open an interruption with a dash, you must close it with a dash. If you open with a comma, close with a comma.', nuance: 'The most common error is mixing punctuation (e.g., opening with a dash and closing with a comma). The second is using only one comma or one dash when a pair is needed to close off the interruption.<br><strong>Strategy:</strong> To check if a phrase is truly non-essential, mentally (or physically) cross out the information between the two punctuation marks. The remaining sentence should still be complete and grammatically sound.', examples: [{ question: "Which sentence is punctuated correctly?", options: ["My brother—a talented musician, is in a band.", "My brother—a talented musician—is in a band."], correct: 1, explanation: 'The first option incorrectly mixes a dash and a comma. The second option correctly uses a pair of dashes to set off the non-essential information.' }] } },
                    { id: 'rule7', title: '7. Apostrophes', content: { cheatCode: 'An apostrophe shows possession (`the student\'s book`) or creates a contraction (`it\'s` for `it is`). Pronouns showing possession (like `its`, `your`, `their`) NEVER have apostrophes.', layman: '<strong>Possession:</strong> To show a noun owns something, add `\'s`. If the noun is plural and already ends in `s`, just add the apostrophe after it (e.g., `the students\' books`).<br><strong>Contraction:</strong> `It\'s`, `you\'re`, `they\'re` are just short ways of saying `it is`, `you are`, and `they are`.', nuance: 'This is one of the most frequently tested concepts. The primary trap is confusing `its` (possession) and `it\'s` (contraction), and `their`/`there`/`they\'re`.<br><strong>Strategy:</strong> When you see `it\'s`, read it as "it is." If the sentence still makes sense, it\'s correct. If not, the answer must be `its`.', examples: [{ question: "Which sentence is correct?", options: ["The dog wagged it's tail.", "The dog wagged its tail."], correct: 1, explanation: 'The first option is incorrect because "it\'s" means "it is." "The dog wagged it is tail" makes no sense. The second option correctly uses the possessive pronoun "its."' }] } },
                    { id: 'rule8', title: '8. Question Marks', content: { cheatCode: 'Use a question mark only for a direct question, not for a statement that describes or reports a question.', layman: 'If the sentence is literally asking something that requires an answer, use a question mark. If the sentence is just *telling you* about a question someone asked or wondered about, end it with a period.', nuance: 'The SAT sets this trap by embedding a question within a larger statement. These often involve words like `whether`, `if`, or `what`. Students see a question being discussed and incorrectly assume a question mark is needed.', examples: [{ question: "Which sentence is punctuated correctly?", options: ["I don't know what the answer is?", "I don't know what the answer is."], correct: 1, explanation: 'The first option is incorrect because it is an indirect question (a statement about a question). It should end with a period, as shown in the second option.' }] } }
                ]
            },
            {
                part: "Part 3: Mastering Core Grammar",
                rules: [
                    { id: 'rule9', title: '9. Subject-Verb Agreement', content: { cheatCode: 'Singular subjects get verbs that end in `-s` (The girl *walks*). Plural subjects get verbs that do not end in `-s` (The girls *walk*).', layman: 'This is the most fundamental rule of grammar, but it\'s the opposite of how nouns work, which can be confusing. For nouns, you add `-s` to make them plural. For verbs, you add `-s` to make them *singular* in the present tense.', nuance: 'The SAT\'s #1 trick is to hide the subject by inserting extra information between it and the verb.<br><strong>Strategy: Cross It Out.</strong> Physically cross out prepositional phrases (starting with `of`, `in`, `for`, etc.) and non-essential clauses that separate the subject from the verb. What\'s left is the core sentence.', examples: [{ question: "Which sentence has correct subject-verb agreement?", options: ["The quality of the recordings are not very good.", "The quality of the recordings is not very good."], correct: 1, explanation: 'The subject is "quality" (singular), not "recordings." Therefore, the singular verb "is" is required.' }] } },
                    { id: 'rule10', title: '10. Advanced S-V Agreement', content: { cheatCode: '`Each`, `every`, and `anyone` are singular. `There is` is for singular nouns; `There are` is for plural nouns. In sentences with reversed order, find the subject *after* the verb.', layman: 'This rule covers the specific tricky subjects the SAT uses to hide agreement errors.', nuance: '<strong>Indefinite Pronouns:</strong> Words like `each`, `every`, `anyone`, `everyone` are always grammatically singular.<br><strong>Collective Nouns:</strong> Nouns like `team`, `committee`, `group` are treated as singular.<br><strong>The "One of the..." Trap:</strong> In the phrase "one of the [PLURAL NOUNS] that/who [VERB]," the verb agrees with the plural noun right before it, not with `one`.', examples: [{ question: "Which sentence is correct?", options: ["Each of the students have a textbook.", "Each of the students has a textbook."], correct: 1, explanation: '"Each" is a singular indefinite pronoun, so it requires the singular verb "has."' }, { question: "Which sentence is correct?", options: ["She is one of the scientists who believe in the theory.", "She is one of the scientists who believes in the theory."], correct: 0, explanation: 'In this structure, "who" refers to "scientists" (plural), not "one." Therefore, the plural verb "believe" is correct.' }] } },
                    { id: 'rule11', title: '11. Pronoun Agreement & Clarity', content: { cheatCode: 'A pronoun (`it`, `they`, `he`, `which`) must clearly and logically refer to a specific noun (its antecedent) from earlier in the sentence.', layman: 'Pronouns are shortcuts. It must be 100% clear who or what the pronoun is replacing. If it could refer to two different things, it\'s wrong.', nuance: 'The SAT tests this in three main ways:<br>1. <strong>Number Agreement:</strong> Singular noun -> singular pronoun; Plural noun -> plural pronoun.<br>2. <strong>Ambiguity:</strong> A pronoun is wrong if it could refer to more than one noun.<br>3. <strong>Missing Antecedent:</strong> A pronoun is wrong if the noun it\'s supposed to refer to doesn\'t actually appear in the text.', examples: [{ question: "Which sentence has a pronoun error?", options: ["When the researcher met with the lab assistant, the researcher was nervous.", "When the researcher met with the lab assistant, he was nervous."], correct: 1, explanation: 'The second sentence has an ambiguous pronoun. Who is "he"? The researcher or the assistant? The first sentence clarifies this by repeating the noun.' }] } },
                    { id: 'rule12', title: '12. Pronoun Case: I vs. Me, Who vs. Whom', content: { cheatCode: 'Use subject pronouns (`I`, `he`, `she`, `who`) for the doer of the action. Use object pronouns (`me`, `him`, `her`, `whom`) for the receiver of the action or after prepositions.', layman: 'This is about choosing the right form of a pronoun. "Me went to the store" sounds wrong because "Me" is a receiver, not a doer. `Who` is a doer like `he`; `whom` is a receiver like `him`.', nuance: 'Use these two simple tricks:<br>1. <strong>Compound Objects:</strong> To decide between `I` and `me`, mentally remove the other person from the sentence.<br>2. <strong>Who vs. Whom (The "He/Him" Test):</strong> Substitute `he` or `him`. If `he` fits, use `who`. If `him` fits, use `whom`.', examples: [{ question: "Which sentence is correct?", options: ["The invitation was sent to my friend and I.", "The invitation was sent to my friend and me."], correct: 1, explanation: 'Remove "my friend and." The sentence becomes "The invitation was sent to me." Therefore, "me" is the correct object pronoun.' }, { question: "Which sentence is correct?", options: ["The award was given to the artist, who the critics praised.", "The award was given to the artist, whom the critics praised."], correct: 1, explanation: 'Test with he/him: "The critics praised him." Since "him" fits, the object pronoun "whom" is correct.' }] } },
                    { id: 'rule13', title: '13. Relative Pronouns', content: { cheatCode: 'Use `who` for people and `which` for things. Use `where` only for physical places and `when` only for times.', layman: 'These are "pointing" words that introduce a description. You have to use the right pointer for the right kind of noun.', nuance: '<strong>`Where` vs. `In Which` (800-Level Trap):</strong> `Where` must refer to a physical, geographical location. For non-physical places—such as a book, a study, a field of research, a time period, or a situation—you must use `in which`.', examples: [{ question: "Which sentence is correct?", options: ["It was a story where the hero saves the day.", "It was a story in which the hero saves the day."], correct: 1, explanation: 'A "story" is not a physical place, so "where" is incorrect. The correct construction is "in which."' }] } },
                    { id: 'rule14', title: '14. Verb Tense & Sequence', content: { cheatCode: 'Keep verb tenses the same unless there is a clear word indicating a time shift. To show one past event happened *before* another past event, use the past perfect (`had done`).', layman: 'Don\'t time travel in your sentences without a reason. If you start a story in the past tense, you should continue in the past tense.', nuance: '<strong>Past Perfect (`had done`):</strong> Marks the *earlier* of two actions that are *both* in the past. If you see `had done`, look for another simple past verb in the sentence. The "had" action always happens first.', examples: [{ question: "Which sentence correctly shows the sequence of past events?", options: ["By the time the police arrived, the thief already escaped.", "By the time the police arrived, the thief had already escaped."], correct: 1, explanation: 'The thief escaping happened *before* the police arrived. Both events are in the past. The past perfect "had escaped" correctly marks the earlier event.' }] } },
                    { id: 'rule15', title: '15. Parallel Structure', content: { cheatCode: 'Items in a list or comparison must be in the same grammatical format.', layman: 'Keep your patterns consistent. If you are listing actions, make them all match. Don\'t say you like `running`, `swimming`, and `to ride` a bike. Say you like `running`, `swimming`, and `riding`.', nuance: 'The SAT tests this with lists and two-part comparisons (`not only...but also`, `either...or`). The grammatical structure of the phrase following the first part must match the structure of the phrase following the second part.', examples: [{ question: "Which sentence has correct parallel structure?", options: ["The plan was designed not only to improve efficiency but also for reducing costs.", "The plan was designed not only to improve efficiency but also to reduce costs."], correct: 1, explanation: 'The first option incorrectly pairs an infinitive ("to improve") with a prepositional phrase ("for reducing"). The second option correctly pairs two infinitives ("to improve" and "to reduce").' }] } }
                ]
            },
            {
                part: "Part 4: Mastering Writing Strategy & Style",
                rules: [
                    { id: 'rule16', title: '16. Concision: Shorter is Better', content: { cheatCode: 'If multiple answer choices are grammatically correct and express the same information, the shortest one is almost always the right answer.', layman: 'The SAT hates wordiness. Don\'t use ten words when five will do. Your goal is to be as clear and direct as possible. Extra words that don\'t add new meaning are just clutter.', nuance: 'This principle specifically targets two common types of errors:<br>1. <strong>Redundancy:</strong> Saying the same thing twice (e.g., "annually each year").<br>2. <strong>Wordy & Passive Phrasing:</strong> The active voice (`The scientist conducted the experiment`) is better than the passive voice (`The experiment was conducted by the scientist`).', examples: [{ question: "Which sentence is the most concise?", options: ["The project was completed annually on a yearly basis.", "The project was completed annually."], correct: 1, explanation: '"Annually" and "yearly" mean the same thing. The second option correctly removes the redundant phrase.' }] } },
                    { id: 'rule17', title: '17. Modifiers: Placement is Everything', content: { cheatCode: 'A descriptive phrase must be placed directly next to the person or thing it is describing.', layman: 'Don\'t let your descriptions float away from what they\'re describing. If a phrase describes the dog, it needs to touch the word "dog." If you put it next to the word "owner," you\'re saying the owner has floppy ears and a tail.', nuance: 'The most tested format is the <strong>Dangling Modifier (Introductory Phrases)</strong>. A sentence will start with a descriptive phrase followed by a comma. The noun that comes immediately after the comma *must* be the thing being described.', examples: [{ question: "Which sentence has a dangling modifier?", options: ["Hoping to get a good grade, the student studied diligently for the exam.", "Hoping to get a good grade, the exam was studied for diligently by the student."], correct: 1, explanation: 'The second sentence is incorrect because the exam was not "hoping to get a good grade"; the student was. The subject "student" must immediately follow the introductory phrase.' }] } },
                    { id: 'rule18', title: '18. Logical Transitions', content: { cheatCode: 'Determine the logical relationship between two sentences *before* looking at the answer choices. Then, pick the transition word that matches.', layman: 'Transition words (`however`, `therefore`, `for example`) are the road signs of an essay. You can\'t just pick a sign because it looks nice; you have to install the one that points in the direction the sentences are actually going.', nuance: '<strong>Strategy: Cross-Out & Define.</strong> Physically cross out the transition word in the passage. Read the sentence before it and the sentence after it. Then, define their relationship in your own simple words ("They are opposites," or "The second one is an example of the first"). Finally, look at the answer choices and pick the word that fits your definition.', examples: [{ question: "Choose the best transition: The team practiced tirelessly for months. ______, they won the championship.", options: ["However", "Therefore"], correct: 1, explanation: 'Winning the championship is a *result* of practicing. Therefore, a cause-and-effect transition is needed.' }] } },
                    { id: 'rule19', title: '19. Relevance & Purpose', content: { cheatCode: 'A sentence is only relevant if it directly supports the specific topic of its paragraph. A detail about penguins doesn\'t belong in a paragraph about polar bears.', layman: 'Every paragraph has a single, main job. Every sentence must be a worker helping to get that job done. If a sentence is off-topic, it\'s a worker from a different factory who has wandered in by mistake—and you need to remove it.', nuance: 'For <strong>Adding/Deleting Sentences</strong> questions, state the paragraph\'s main point in your own words. Then, see if the sentence in question directly supports that point. For <strong>Placing Sentences</strong> questions, look for "pointer" words like "this discovery" or pronouns that refer to something in the previous sentence.', examples: [{ question: 'A paragraph describes the challenges of space travel. Should this sentence be added: "Many astronauts enjoy the view of Earth from space."', options: ["Yes, it adds an interesting detail.", "No, it blurs the paragraph's focus."], correct: 1, explanation: 'The paragraph\'s topic is the *challenges* of space travel. The astronauts\' enjoyment is off-topic and should not be added.' }] } }
                ]
            },
            {
                part: "Part 5: Mastering Diction and Comparisons",
                rules: [
                    { id: 'rule20', title: '20. Diction and Idioms', content: { cheatCode: 'Use words according to their precise dictionary definitions and be sure to use the correct prepositions that idiomatically follow certain verbs or adjectives.', layman: 'This is about choosing the right word for the job. You don\'t say you have an `affect` on something; you have an `effect`. English also has "word families"—words that always go with specific partners (e.g., `capable of`, `different from`).', nuance: 'The SAT frequently tests pairs like `affect/effect` (verb/noun), `than/then` (comparison/time), and `precede/proceed` (come before/go forward). Also, word choice must match the formal, academic tone of the passage (e.g., use "many" instead of "a lot of").', examples: [{ question: "Which sentence is correct?", options: ["The new law will have a strong affect on the economy.", "The new law will have a strong effect on the economy."], correct: 1, explanation: '"Effect" is a noun meaning "result" or "impact." "Affect" is a verb meaning "to influence." The sentence requires a noun.' }] } },
                    { id: 'rule21', title: '21. Logical Comparisons', content: { cheatCode: 'You must compare apples to apples, not apples to oranges. Compare actions to actions, people to people, and things to things.', layman: 'A sentence like "A cheetah\'s speed is faster than a turtle" is wrong. It\'s illogically comparing speed (a concept) to a turtle (an animal). You must compare a cheetah\'s speed to a turtle\'s speed.', nuance: 'Watch for comparisons between a person\'s work and the person themselves. To fix a faulty comparison, the SAT often uses the phrases `that of` (for singular comparisons) or `those of` (for plural comparisons).', examples: [{ question: "Which sentence makes a logical comparison?", options: ["The paintings of Monet are more famous than Van Gogh.", "The paintings of Monet are more famous than those of Van Gogh."], correct: 1, explanation: 'The first sentence illogically compares paintings to a person. The second correctly compares paintings to paintings by using "those of" as a substitute for "the paintings of."' }] } }
                ]
            },
            {
                part: "Part 6: Mastering the Reading Test",
                rules: [
                    { id: 'rule22', title: '22. Words in Context', content: { cheatCode: 'The correct answer is almost never the most common definition of the word. Cover the word, read the sentence, and find the answer choice that is the most logical and precise synonym for that specific context.', layman: 'Words have multiple personalities. The word `run` means something different in "run a race," "run a business," and "a run in your stockings." You must look at the specific job the word is doing in that exact sentence.', nuance: 'This is a test of precision. The wrong answers will often be more common definitions of the word that don\'t fit the passage\'s logic.<br><strong>Strategy: Treat it like a blank.</strong> Forget the original word for a moment. Read the sentence and determine what kind of word needs to fit in the blank to make logical sense. Then, test the answer choices.', examples: [{ question: 'In the sentence, "The scientist wanted to **address** the concerns of the committee," what does "address" most nearly mean?', options: ["Locate", "Speak about"], correct: 1, explanation: 'The scientist isn\'t looking for a location. They are going to speak about or deal with the concerns.' }] } },
                    { id: 'rule23', title: '23. Command of Evidence', content: { cheatCode: 'Answer the first question, then find the lines that serve as the direct, explicit proof for your answer. The correct evidence is the reason your first answer is true.', layman: 'The SAT asks, "What does the author think?" and then immediately follows with, "Prove it." You have to find the quotation that acts as the "smoking gun" for your first answer.', nuance: 'A common trap is an evidence choice that is on the same topic as your answer but doesn\'t *directly prove it*. <br><strong>Strategy 2 (Check evidence first):</strong> Look at the four evidence options in Q2 *first*. For each one, determine what claim it supports. Then, find the answer choice in Q1 that matches one of your summaries.', examples: [{ question: "If Q1 asks why lions are successful hunters, and the answer is 'cooperative strategies,' which is the best evidence for Q2?", options: ['"Lions are the only cats to live in social groups called prides."', '"By hunting in coordinated groups, prides are able to take down prey much larger than any single lion could handle alone."'], correct: 1, explanation: 'The second option provides direct proof of *how* cooperative strategies lead to success. The first option is related but doesn\'t explain their hunting success.' }] } },
                    { id: 'rule24', title: '24. Analyzing Purpose, Tone, and Perspective', content: { cheatCode: 'The author\'s purpose is *why* they wrote the passage. Their tone is their *attitude* toward the subject. Pay close attention to adjectives and adverbs to figure it out.', layman: 'You need to figure out the author\'s agenda and their "vibe."<br><strong>Purpose:</strong> Are they trying to inform, persuade, critique, entertain, or reflect?<br><strong>Tone:</strong> Are they excited (`thankfully`, `remarkable`), skeptical (`supposedly`, `so-called`), critical (`unfortunately`, `flawed`), or objective (neutral, factual language)?', nuance: 'The SAT asks you to infer the author\'s intent and attitude, which are revealed through specific word choices. Wrong answers will mischaracterize the author\'s stance (e.g., calling a critical passage "supportive").', examples: [{ question: 'What is the tone of this sentence: "The researcher\'s **supposed** breakthrough, which relied on a **shockingly small** sample size, has been met with **widespread doubt** from his peers."', options: ["Supportive and admiring", "Critical and skeptical"], correct: 1, explanation: 'The words "supposed," "shockingly small," and "widespread doubt" all indicate a negative and skeptical attitude towards the research.' }] } },
                    { id: 'rule25', title: '25. Reading Infographics (Charts & Graphs)', content: { cheatCode: 'First, read the title, axes (including units), and legend/key. Then, state the graph\'s main point or overall trend in one simple sentence before looking at the questions.', layman: 'Don\'t just glance at the picture. Take 15 seconds to understand what the graph is actually measuring. Is it showing change over time? A comparison between groups? Once you know its story, you can easily spot which answer choices are telling the truth about it.', nuance: 'Common traps include misreading the units, confusing correlation with causation, and ignoring the overall trend in favor of one specific data point.', examples: [{ question: 'A graph shows profits rising from 2010-2015, then falling sharply in 2016. What is the best interpretation?', options: ["The company's profits increased.", "The company's profits generally rose until a sharp decline in 2016."], correct: 1, explanation: 'The first option is an incomplete interpretation because it ignores the significant drop. The second option accurately describes the overall trend.' }] } },
                    { id: 'rule26', title: '26. Strategy for Paired Passages', content: { cheatCode: 'Read Passage 1 and summarize its main argument. Then read Passage 2, constantly asking, "How does this relate to Passage 1? Does it agree, disagree, or just discuss something different?"', layman: 'Think of it as listening to two people debate. You need to figure out their relationship. Are they on the same side but offering different evidence? Are they in direct opposition? Is Passage 2 critiquing the idea mentioned in Passage 1?', nuance: 'Most questions for this passage type will focus on the relationship between the two texts.<br><strong>Strategy: Map It Out.</strong> After reading each passage, jot down a 3-5 word summary of the author\'s main claim (e.g., "P1: Pro-nuclear energy"; "P2: Nuclear energy unsafe"). Then, note the relationship (e.g., "P2 directly disagrees with P1").', examples: [{ question: "P1 argues Shakespeare's plays were written by Bacon. P2 provides evidence that Shakespeare of Stratford was the author. What is the relationship?", options: ["Passage 2 supports the theory in Passage 1.", "Passage 2 refutes the theory in Passage 1."], correct: 1, explanation: '"Refutes" means "disproves." Passage 2 presents evidence against the theory in Passage 1, so it refutes it.' }] } },
                    { id: 'rule27', title: '27. Advanced Reading Analysis', content: { cheatCode: 'A correct inference *must be true* based on the text. When analyzing an argument, identify the main claim and how the author uses specific evidence to support it.', layman: 'This rule covers the trickiest analytical skills.<br><strong>Inferences:</strong> An inference isn\'t a wild guess. It\'s a small, direct logical step. If the passage says, "The ground was wet," you can infer it rained. You cannot infer there was a thunderstorm.<br><strong>Arguments:</strong> Think like a detective. What is the author trying to prove (the claim)? What facts or stories are they using to prove it (the evidence)? How do they connect the two (the reasoning)?', nuance: '<strong>Supportable Inferences:</strong> The SAT will present trap answers that are plausible but not directly supported. Always ask, "Can I prove this *only* with the information given?"<br><strong>Distinguishing Voices:</strong> In passages that discuss multiple viewpoints (e.g., Scientist A vs. Scientist B), be careful to attribute claims to the correct person. Trap answers will often correctly state a viewpoint but attribute it to the wrong source.', examples: [{ question: 'If a passage states, "The subject showed a faster heart rate and increased perspiration when shown the color red," what can you infer?', options: ["The subject was scared of the color red.", "The color red elicited a physiological response in the subject."], correct: 1, explanation: 'You can only infer what is directly supported by the text. The text describes a physiological response, not a specific emotion like fear.' }] } }
                ]
            }
        ];

        // --- Global Application State --- //
        const appState = {
            currentView: 'dashboard',
            lastViewedRule: localStorage.getItem('lastViewedRule') || 'rule1',
            lastTestResult: JSON.parse(localStorage.getItem('lastTestResult')) || null,
            testState: {
                module: 1,
                currentQuestionIndex: 0,
                questions: [],
                userAnswers: [],
                module1Correct: 0,
                module2Correct: 0,
                timerInterval: null,
                timeRemaining: 32 * 60,
            }
        };

        const loadingTexts = [
            "Don't worry, a comma splice is just a sentence with commitment issues.",
            "Finding the main idea... it's not hiding, we promise.",
            "Sharpening our pencils and your wit.",
            "Calculating the velocity of a dangling modifier...",
            "Remember: 'Shorter is better' applies to answers, not your study breaks.",
            "Waking up the AI... it's not a morning person either.",
            "Polishing the parallel structures.",
            "Making sure our semicolons are more than just fancy commas."
        ];
        
        // --- Core App Logic & Initialization --- //
        document.addEventListener('DOMContentLoaded', () => {
            const appContent = document.getElementById('app-content');
            const loadingScreen = document.getElementById('loading-screen');
            const loadingTextEl = document.getElementById('loading-text');

            loadingTextEl.textContent = loadingTexts[Math.floor(Math.random() * loadingTexts.length)];
            
            setTimeout(() => {
                loadingScreen.style.opacity = '0';
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                    appContent.classList.remove('hidden');
                    appContent.style.opacity = '1';
                }, 500);
            }, 1500);

            initializeRouter();
            initializeStudyGuide();
            initializePracticeTest();
            updateDashboard();
        });

        function showView(viewId) {
            document.querySelectorAll('.view').forEach(view => view.classList.add('hidden'));
            document.getElementById(viewId).classList.remove('hidden');
            appState.currentView = viewId.replace('view-', '');
             window.scrollTo(0, 0);
        }
        
        function updateDashboard() {
             if (appState.lastTestResult) {
                const dashboardContent = document.getElementById('dashboard-content');
                let scoreCard = dashboardContent.querySelector('#score-card');
                if (!scoreCard) {
                    scoreCard = document.createElement('div');
                    scoreCard.id = 'score-card';
                    scoreCard.className = 'md:col-span-2 bg-white p-6 rounded-lg shadow-lg border border-gray-200';
                    dashboardContent.appendChild(scoreCard);
                }
                scoreCard.innerHTML = `
                    <h3 class="text-2xl font-bold text-gray-700 mb-4 text-center">Your Last Test Result</h3>
                    <p class="text-6xl font-bold text-center text-[var(--primary-accent)]">${appState.lastTestResult.scaledScore}</p>
                    <p class="text-center text-gray-500 mt-2">${appState.lastTestResult.totalCorrect} / 54 Correct</p>
                `;
            }
        }

        function initializeRouter() {
            document.querySelectorAll('.nav-link, .start-btn, .home-btn').forEach(button => {
                button.addEventListener('click', (e) => {
                    const view = e.currentTarget.dataset.view || 'dashboard';
                    showView(`view-${view}`);
                });
            });
        }
        
        // --- Study Guide Logic --- //
        function initializeStudyGuide() {
            const navigationMenu = document.getElementById('navigation-menu');
            const contentArea = document.getElementById('content-area');
            const sidebar = document.getElementById('sidebar');
            const menuToggle = document.getElementById('menu-toggle');

            function renderNavigation() {
                navigationMenu.innerHTML = '';
                guideData.forEach((part) => {
                    const partDiv = document.createElement('div');
                    partDiv.className = 'mb-4';
                    const partTitle = document.createElement('h3');
                    partTitle.className = 'text-lg font-semibold text-gray-700 mb-2 cursor-pointer p-2 rounded-md hover:bg-gray-200';
                    partTitle.textContent = part.part;
                    partDiv.appendChild(partTitle);
                    const ruleList = document.createElement('ul');
                    ruleList.className = 'space-y-1 ml-2 hidden';
                    part.rules.forEach((rule) => {
                        const listItem = document.createElement('li');
                        const link = document.createElement('a');
                        link.href = '#';
                        link.textContent = rule.title;
                        link.className = 'block p-2 rounded-md text-sm text-[var(--text-secondary)] hover:bg-[var(--border-color)] sidebar-link';
                        link.dataset.id = rule.id;
                        link.addEventListener('click', (e) => {
                            e.preventDefault();
                            renderContent(rule);
                            document.querySelectorAll('.sidebar-link').forEach(l => l.classList.remove('active'));
                            link.classList.add('active');
                            appState.lastViewedRule = rule.id;
                            localStorage.setItem('lastViewedRule', rule.id);
                            if (window.innerWidth < 768) {
                                sidebar.classList.add('-translate-x-full');
                            }
                        });
                        listItem.appendChild(link);
                        ruleList.appendChild(listItem);
                    });
                    partDiv.appendChild(ruleList);
                    navigationMenu.appendChild(partDiv);
                    partTitle.addEventListener('click', () => {
                        ruleList.classList.toggle('hidden');
                    });
                });
            }

            function renderContent(rule) {
                const ruleData = rule.content;
                let examplesHtml = ruleData.examples.map((ex, index) => `
                    <div class="mt-6 bg-gray-50 p-4 rounded-lg border border-gray-200">
                        <p class="font-semibold mb-3">Interactive Example ${index + 1}:</p>
                        <p class="mb-4 text-gray-800">${ex.question}</p>
                        <div class="space-y-2" id="quiz-${rule.id}-${index}">
                            ${ex.options.map((opt, optIndex) => `
                                <button class="w-full text-left p-3 rounded-lg quiz-option bg-white" data-option="${optIndex}">
                                    <span class="font-bold mr-2">${String.fromCharCode(65 + optIndex)}</span> ${opt}
                                </button>
                            `).join('')}
                        </div>
                        <div id="feedback-${rule.id}-${index}" class="mt-3 text-sm font-medium p-3 rounded-md hidden"></div>
                    </div>
                `).join('');

                contentArea.innerHTML = `
                    <div class="bg-white p-6 md:p-8 rounded-xl shadow-md border border-gray-200">
                        <h2 class="text-3xl font-bold text-[var(--primary-accent)] mb-6">${rule.title}</h2>
                        <div class="space-y-8">
                            <div><h4 class="text-xl font-semibold text-gray-800 mb-2">Cheat-Code</h4><p class="p-4 bg-amber-50 rounded-md text-yellow-800 italic">${ruleData.cheatCode}</p></div>
                            <div><h4 class="text-xl font-semibold text-gray-800 mb-2">Layman's Explanation</h4><p class="text-[var(--text-secondary)] leading-relaxed">${ruleData.layman}</p></div>
                            <div><h4 class="text-xl font-semibold text-gray-800 mb-2">The Nuance & How It's Tested</h4><div class="text-[var(--text-secondary)] leading-relaxed">${ruleData.nuance}</div></div>
                            <div><h3 class="text-2xl font-bold text-gray-800 border-b-2 border-[var(--primary-accent)] pb-2 mb-4">Test Your Knowledge</h3>${examplesHtml}</div>
                        </div>
                    </div>`;
                
                ruleData.examples.forEach((ex, index) => {
                    const quizContainer = document.getElementById(`quiz-${rule.id}-${index}`);
                    quizContainer.addEventListener('click', (e) => {
                        const button = e.target.closest('.quiz-option');
                        if (button && !button.disabled) {
                            const selectedOption = parseInt(button.dataset.option);
                            const feedbackEl = document.getElementById(`feedback-${rule.id}-${index}`);
                            Array.from(quizContainer.children).forEach(child => { child.disabled = true; });
                            
                            feedbackEl.classList.remove('hidden');
                            if (selectedOption === ex.correct) {
                                button.classList.add('correct');
                                feedbackEl.innerHTML = `<span class="font-bold">Correct!</span> ${ex.explanation}`;
                                feedbackEl.className = 'mt-3 text-sm font-medium p-3 rounded-md bg-green-100 text-green-800';
                            } else {
                                button.classList.add('incorrect');
                                quizContainer.children[ex.correct].classList.add('correct');
                                feedbackEl.innerHTML = `<span class="font-bold">Not quite.</span> ${ex.explanation}`;
                                feedbackEl.className = 'mt-3 text-sm font-medium p-3 rounded-md bg-red-100 text-red-800';
                            }
                        }
                    });
                });
            }
            
            menuToggle.addEventListener('click', () => sidebar.classList.toggle('-translate-x-full'));
            renderNavigation();
            const lastViewedLink = document.querySelector(`.sidebar-link[data-id="${appState.lastViewedRule}"]`);
            if (lastViewedLink) {
                lastViewedLink.closest('ul').classList.remove('hidden');
                lastViewedLink.click();
            } else {
                 const firstLink = navigationMenu.querySelector('.sidebar-link');
                 if(firstLink) firstLink.click();
            }
        }

        // --- Practice Test Logic --- //
        function initializePracticeTest() {
            document.getElementById('start-test-btn').addEventListener('click', () => startTestModule(1));
            document.getElementById('next-question-btn').addEventListener('click', handleNextQuestion);
            document.getElementById('review-answers-btn').addEventListener('click', startReview);
            document.getElementById('prev-review-btn').addEventListener('click', () => navigateReview(-1));
            document.getElementById('next-review-btn').addEventListener('click', () => navigateReview(1));
        }

        async function startTestModule(moduleNumber) {
            const loadingScreen = document.getElementById('loading-screen');
            const loadingTextEl = document.getElementById('loading-text');
            showView('loading-screen');
            loadingScreen.style.display = 'flex';
            loadingScreen.style.opacity = '1';
            
            loadingTextEl.textContent = loadingTexts[Math.floor(Math.random() * loadingTexts.length)];

            appState.testState.module = moduleNumber;
            const startIndex = (moduleNumber - 1) * 27;
            appState.testState.currentQuestionIndex = startIndex;
            
            if (moduleNumber === 1) {
                appState.testState.questions = [];
                appState.testState.userAnswers = [];
            }

            let difficulty = 'medium';
            if (moduleNumber === 2) {
                const score = appState.testState.module1Correct;
                if (score > 18) difficulty = 'hard';
                else if (score < 10) difficulty = 'easy';
            }
            
            try {
                 const newQuestions = await generateTestModule(27, difficulty);
                 if (newQuestions.length < 27) throw new Error("AI failed to generate enough questions.");
                 appState.testState.questions.push(...newQuestions);

                loadingScreen.style.opacity = '0';
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                    showView('view-practice-test');
                    renderTestQuestion();
                    startTimer();
                }, 500);
            } catch (error) {
                 loadingScreen.innerHTML = `<div class="text-center"><h2 class="text-2xl font-bold mb-4 text-red-400">Generation Failed</h2><p class="text-gray-300">Could not generate the practice test. This might be due to an API key issue or network error.</p><button class="home-btn mt-6 bg-white text-gray-800 font-bold py-2 px-6 rounded-full">Return Home</button></div>`;
                 document.querySelector('#loading-screen .home-btn').addEventListener('click', () => {
                    loadingScreen.style.opacity = '0';
                    setTimeout(() => {
                        loadingScreen.style.display = 'none';
                        showView('view-dashboard');
                    }, 500);
                 });
            }
        }
        
        function buildSatPrompt(skill, difficulty) {
            let prompt = `Generate a unique, high-quality, Digital SAT-style Reading and Writing question.
            - The question must test the skill: "${skill}".
            - The difficulty should be: "${difficulty}".
            - The passage should be 25-150 words and written at a grade 9-12 complexity level.
            - The question must have one clearly correct answer and three plausible but incorrect distractors that reflect common SAT traps.
            - You MUST respond with only the raw JSON object, without any additional text, explanations, or markdown formatting.
            - The JSON object must have these exact keys: "passage", "question", "options" (an array of 4 strings), "correctAnswerIndex" (a number 0-3), and "explanation" (a brief explanation of the correct answer).
            - Ensure the answer choices have similar lengths (differ by no more than 2 words or 15 characters).
            `;

            if (skill === "Words in Context") {
                prompt += "\n- For the distractors, provide three other valid dictionary senses of the target word that do not fit the context.";
            } else if (skill === "Central Ideas & Details") {
                prompt += "\n- For the distractors, provide one choice that is too narrow, one that is too broad, and one that is off-topic.";
            } else if (skill === "Transitions") {
                prompt += "\n- For the distractors, provide one choice representing a 'contrast' relationship (e.g., however), one representing 'continuation' (e.g., moreover), and one representing 'cause-and-effect' (e.g., therefore), ensuring only one logically fits.";
            } else if (skill === "Command of Evidence - Textual") {
                 prompt += "\n- The question should ask which quotation from the passage best supports a specific claim. The correct answer must be a direct quote that logically proves the claim."
            }

            return prompt;
        }


        async function generateTestModule(numQuestions, difficulty) {
            const domainDistribution = [
                ...Array(7).fill("Information & Ideas"),
                ...Array(8).fill("Craft & Structure"),
                ...Array(5).fill("Expression of Ideas"),
                ...Array(7).fill("Standard English Conventions")
            ];
            
            const skillTemplates = {
                "Information & Ideas": ["Central Ideas & Details", "Command of Evidence - Textual", "Inferences"],
                "Craft & Structure": ["Words in Context", "Text Structure & Purpose"],
                "Expression of Ideas": ["Rhetorical Synthesis", "Transitions"],
                "Standard English Conventions": ["Boundaries", "Form, Structure, & Sense"]
            };

            const promises = [];

            for (let i = 0; i < numQuestions; i++) {
                const domain = domainDistribution[i];
                const skillsInDomain = skillTemplates[domain];
                const skill = skillsInDomain[Math.floor(Math.random() * skillsInDomain.length)];
                
                const prompt = buildSatPrompt(skill, difficulty);
                promises.push(generateQuestionFromAPI(prompt));
            }
            const results = await Promise.all(promises);
            const validResults = results.filter(q => q && q.passage && q.question && Array.isArray(q.options) && q.options.length === 4);
            
            if (validResults.length < numQuestions) {
                console.warn(`Fewer than expected questions generated (${validResults.length}/${numQuestions}). Using dummy data for the rest.`);
                const dummyQuestion = {passage:"This is a fallback question because the AI model failed to generate a valid response. Please check your API key and endpoint.", question:"Which option indicates a system error?", options:["Option A", "Option B", "This one", "Option D"], correctAnswerIndex: 2, explanation: "This is a fallback."};
                while(validResults.length < numQuestions) {
                    validResults.push(dummyQuestion);
                }
            }
            return validResults;
        }

        async function generateQuestionFromAPI(prompt) {
            const azureApiKey = "C6QRYwSP33Cr4XYHQx4UKec1uJDrb1cQV3SjXs4vDfKpshRummDfJQQJ99BFACHYHv6XJ3w3AAAAACOGeK42";
            const azureApiUrl = "https://vedan-mbn0ifew-eastus2.cognitiveservices.azure.com/openai/deployments/decaide_sat/chat/completions?api-version=2024-02-15-preview";
            
            const payload = {
                "messages": [
                    { "role": "system", "content": "You are an expert SAT question generator. You will create a unique, high-quality, Digital SAT-style Reading and Writing question based on the user's request. You MUST respond with only the raw JSON object, without any additional text, explanations, or markdown formatting." },
                    { "role": "user", "content": prompt }
                ],
                "response_format": { "type": "json_object" },
                "temperature": 0.8,
                "max_tokens": 800
            };

            try {
                const response = await fetch(azureApiUrl, {
                    method: 'POST',
                    headers: {
                        'api-key': azureApiKey,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(payload)
                });

                if (!response.ok) {
                    const errorBody = await response.text();
                    throw new Error(`Azure API Error: ${response.status} - ${errorBody}`);
                }

                const result = await response.json();

                if (result.choices && result.choices[0] && result.choices[0].message && result.choices[0].message.content) {
                    let jsonString = result.choices[0].message.content;
                    
                    const startIndex = jsonString.indexOf('{');
                    const endIndex = jsonString.lastIndexOf('}');
                    if (startIndex !== -1 && endIndex !== -1) {
                        jsonString = jsonString.substring(startIndex, endIndex + 1);
                    }

                    try {
                        return JSON.parse(jsonString);
                    } catch (parseError) {
                         console.error("JSON Parse Error:", parseError, "for string:", jsonString);
                         throw new Error("Failed to parse JSON response from API.");
                    }
                } else {
                    console.error("Invalid response structure from Azure API:", result);
                    throw new Error("Invalid response structure from Azure API");
                }
            } catch (error) {
                console.error("Error in generateQuestionFromAPI:", error);
                return null;
            }
        }

        function renderTestQuestion() {
            const state = appState.testState;
            const q = state.questions[state.currentQuestionIndex];
            
            document.getElementById('test-section-title').textContent = `Module ${state.module}: Reading and Writing`;
            document.getElementById('test-passage-container').innerHTML = `<p class="text-lg leading-relaxed whitespace-pre-wrap">${q.passage}</p>`;
            
            const questionHtml = `
                <div class="flex-grow flex flex-col">
                    <div class="mb-4">
                       <p class="font-semibold text-lg">${q.question}</p>
                    </div>
                    <div class="space-y-3" id="test-options-container">
                        ${q.options.map((opt, index) => `
                            <button class="w-full text-left p-4 rounded-lg sat-test-option flex items-start space-x-4" data-index="${index}">
                                <span class="flex-shrink-0 mt-1 w-6 h-6 rounded-full border-2 border-gray-400 flex items-center justify-center font-bold text-gray-500">${String.fromCharCode(65 + index)}</span>
                                <span class="flex-1">${opt}</span>
                            </button>
                        `).join('')}
                    </div>
                </div>
            `;
            document.getElementById('test-question-container').innerHTML = questionHtml;
            document.getElementById('question-counter').textContent = `Question ${state.currentQuestionIndex % 27 + 1} of 27`;
            const progressIndex = state.currentQuestionIndex;
            document.getElementById('test-progress-bar-top').style.width = `${((progressIndex + 1) / 54) * 100}%`;

            document.querySelectorAll('.sat-test-option').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const selectedBtn = e.currentTarget;
                    document.querySelectorAll('.sat-test-option').forEach(b => b.classList.remove('selected'));
                    selectedBtn.classList.add('selected');
                    appState.testState.userAnswers[state.currentQuestionIndex] = parseInt(selectedBtn.dataset.index);
                });
            });
        }
        
        function handleNextQuestion() {
            const moduleQuestionIndex = appState.testState.currentQuestionIndex % 27;
            if (moduleQuestionIndex < 26) {
                appState.testState.currentQuestionIndex++;
                renderTestQuestion();
            } else {
                endModule();
            }
        }

        function endModule() {
            clearInterval(appState.testState.timerInterval);
            let score = 0;
            const startIndex = (appState.testState.module - 1) * 27;
            const endIndex = startIndex + 27;
            for(let i = startIndex; i < endIndex; i++) {
                if (appState.testState.userAnswers[i] === appState.testState.questions[i].correctAnswerIndex) {
                    score++;
                }
            }

            if (appState.testState.module === 1) {
                appState.testState.module1Correct = score;
                showView('loading-screen');
                document.getElementById('loading-screen').style.display = 'flex';
                document.getElementById('loading-screen').style.opacity = '1';
                document.getElementById('loading-text').textContent = `Module 1 complete. Calibrating Module 2 based on your performance...`;
                setTimeout(() => startTestModule(2), 2500);
            } else {
                appState.testState.module2Correct = score;
                displayScoreReport();
            }
        }
        
        function displayScoreReport() {
            showView('view-score-report');
            const totalCorrect = appState.testState.module1Correct + appState.testState.module2Correct;
            const totalQuestions = 54;
            
            const rawScore = totalCorrect;
            const baseScore = 200;
            const maxScore = 800;
            const scaledScore = Math.round(baseScore + (maxScore - baseScore) * Math.pow(rawScore / totalQuestions, 0.75));
            
            const resultData = {
                scaledScore,
                totalCorrect,
                module1Correct: appState.testState.module1Correct,
                module2Correct: appState.testState.module2Correct
            };
            appState.lastTestResult = resultData;
            localStorage.setItem('lastTestResult', JSON.stringify(resultData));
            updateDashboard();

            document.getElementById('scaled-score').textContent = scaledScore;
            document.getElementById('module1-correct').textContent = `Module 1: ${appState.testState.module1Correct} / 27`;
            document.getElementById('module2-correct').textContent = `Module 2: ${appState.testState.module2Correct} / 27`;
            document.getElementById('total-correct').textContent = `${totalCorrect} / 54 Correct`;
            
            const ctx = document.getElementById('scoreChart').getContext('2d');
            if(window.myScoreChart) window.myScoreChart.destroy();
            window.myScoreChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['Module 1', 'Module 2'],
                    datasets: [{
                        label: '# of Correct Answers',
                        data: [appState.testState.module1Correct, appState.testState.module2Correct],
                        backgroundColor: ['rgba(197, 167, 106, 0.6)', 'rgba(0, 122, 122, 0.6)'],
                        borderColor: ['rgba(197, 167, 106, 1)', 'rgba(0, 122, 122, 1)'],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: { y: { beginAtZero: true, max: 27, ticks: { color: 'var(--text-secondary)' } }, x: { ticks: { color: 'var(--text-primary)', font: { weight: 'bold' } } } },
                    plugins: { legend: { display: false } }
                }
            });
        }

        function startTimer() {
            const timerEl = document.getElementById('test-timer');
            appState.testState.timeRemaining = 32 * 60; 
            clearInterval(appState.testState.timerInterval);
            appState.testState.timerInterval = setInterval(() => {
                appState.testState.timeRemaining--;
                const minutes = Math.floor(appState.testState.timeRemaining / 60);
                const seconds = appState.testState.timeRemaining % 60;
                timerEl.textContent = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
                if (appState.testState.timeRemaining <= 0) {
                    clearInterval(appState.testState.timerInterval);
                    alert("Time's up!");
                    endModule();
                }
            }, 1000);
        }

        // --- Review Mode Logic --- //
        let reviewIndex = 0;
        function startReview() {
            reviewIndex = 0;
            showView('view-review-test');
            renderReviewQuestion();
        }

        function navigateReview(direction) {
            reviewIndex += direction;
            const totalQuestions = appState.testState.questions.length;
            if (reviewIndex < 0) reviewIndex = 0;
            if (reviewIndex >= totalQuestions) reviewIndex = totalQuestions - 1;
            renderReviewQuestion();
        }

        function renderReviewQuestion() {
            const q = appState.testState.questions[reviewIndex];
            const userAnswer = appState.testState.userAnswers[reviewIndex];
            const isCorrect = userAnswer === q.correctAnswerIndex;

            document.getElementById('review-passage-container').innerHTML = `<p class="text-lg leading-relaxed whitespace-pre-wrap">${q.passage}</p>`;
            
            const questionHtml = `
                <div class="flex-grow flex flex-col">
                    <div class="mb-4"><p class="font-semibold text-lg">${q.question}</p></div>
                    <div class="space-y-3">
                        ${q.options.map((opt, index) => {
                            let classes = 'sat-test-option';
                            if (index === q.correctAnswerIndex) classes += ' correct';
                            if (index === userAnswer && !isCorrect) classes += ' incorrect';
                            if (index === userAnswer) classes += ' selected';
                            return `<div class="w-full text-left p-4 rounded-lg flex items-start space-x-4 ${classes}">
                                <span class="flex-shrink-0 mt-1 w-6 h-6 rounded-full border-2 flex items-center justify-center font-bold">${String.fromCharCode(65 + index)}</span>
                                <span class="flex-1">${opt}</span>
                            </div>`;
                        }).join('')}
                    </div>
                    <div class="mt-4 p-4 rounded-lg bg-blue-50 border border-blue-200">
                        <h4 class="font-bold text-blue-800">Explanation:</h4>
                        <p class="text-blue-700">${q.explanation}</p>
                    </div>
                </div>`;
            
            document.getElementById('review-question-container').innerHTML = questionHtml;
            document.getElementById('review-question-counter').textContent = `Question ${reviewIndex + 1} of ${appState.testState.questions.length}`;
            document.getElementById('review-progress-bar').style.width = `${((reviewIndex + 1) / appState.testState.questions.length) * 100}%`;
            
            document.getElementById('prev-review-btn').disabled = reviewIndex === 0;
            document.getElementById('next-review-btn').disabled = reviewIndex === appState.testState.questions.length - 1;
        }
    </script>
</body>
</html>
