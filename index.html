<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DecA(I)de SAT Prep</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    },
                    borderRadius: {
                        'device': '80px',
                        'panel': '48px',
                        'pill': '999px',
                    },
                    spacing: {
                        '18': '4.5rem',
                        '22': '5.5rem',
                        '26': '6.5rem',
                        '30': '7.5rem',
                    }
                }
            }
        }
    </script>
    <style>
        /* Premium Design System */
        :root {
            --bg-outer: #0a0a0a;
            --bg-matte: #f8f9fa;
            --bg-panel: #ffffff;
            --text-primary: #1a1a1a;
            --text-secondary: #6b7280;
            --accent-primary: #2563eb;
            --accent-hover: #1d4ed8;
            --border-light: #e5e7eb;
            --border-medium: #d1d5db;
        }

        body {
            font-family: 'Inter', system-ui, sans-serif;
            background: var(--bg-outer);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Device Frame & Panel System */
        .device-frame {
            min-height: 100vh;
            background: var(--bg-outer);
            padding: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .content-panel {
            width: 100%;
            max-width: 1400px;
            min-height: calc(100vh - 48px);
            background: var(--bg-matte);
            border-radius: 80px;
            padding: 16px;
            position: relative;
        }

        .inner-panel {
            width: 100%;
            height: 100%;
            min-height: calc(100vh - 80px);
            background: var(--bg-panel);
            border-radius: 48px;
            position: relative;
            overflow: hidden;
        }

        /* Typography System */
        .headline {
            font-size: 64px;
            font-weight: 700;
            line-height: 1.2;
            letter-spacing: -0.02em;
            color: var(--text-primary);
            margin: 0;
        }

        .nav-text {
            font-size: 14px;
            font-weight: 500;
            line-height: 1.4;
            color: var(--text-primary);
        }

        .body-text {
            font-size: 12px;
            font-weight: 400;
            line-height: 1.5;
            color: var(--text-secondary);
            max-width: 360px;
        }

        .logo-text {
            font-size: 18px;
            font-weight: 700;
            line-height: 1.2;
            color: var(--text-primary);
        }

        /* Component Styles */
        .nav-pill {
            border: 1px solid var(--border-medium);
            border-radius: 999px;
            padding: 12px 24px;
            display: inline-flex;
            align-items: center;
            gap: 32px;
        }

        .nav-link {
            color: var(--text-primary);
            text-decoration: none;
            transition: color 0.2s ease;
        }

        .nav-link:hover {
            color: var(--accent-primary);
        }

        .btn-primary {
            background: var(--accent-primary);
            color: white;
            border: none;
            border-radius: 24px;
            padding: 12px 24px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary:hover {
            background: var(--accent-hover);
        }

        .language-dropdown {
            border: 1px solid var(--border-medium);
            border-radius: 24px;
            padding: 8px 16px;
            background: white;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .contact-link {
            color: var(--text-primary);
            text-decoration: none;
            font-size: 16px;
            font-weight: 500;
            line-height: 1.3;
            border-bottom: 1px solid var(--text-primary);
            transition: border-color 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .contact-link:hover {
            border-bottom-width: 2px;
        }

        .crazy-mode-toggle {
            position: absolute;
            bottom: 32px;
            right: 32px;
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 14px;
            font-weight: 500;
        }

        .toggle-switch {
            border: 1px solid var(--border-medium);
            border-radius: 16px;
            padding: 8px;
            background: white;
            cursor: pointer;
            min-width: 60px;
            text-align: center;
            font-size: 12px;
        }

        /* Layout System */
        .two-column-layout {
            display: flex;
            height: 100%;
            min-height: calc(100vh - 80px);
        }

        .text-column {
            flex: 0 0 40%;
            padding: 64px 0 0 80px;
            display: flex;
            flex-direction: column;
        }

        .visual-column {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .header-nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 32px 32px;
            width: 100%;
            position: absolute;
            top: 0;
            left: 0;
            z-index: 10;
        }

        .nav-left {
            display: flex;
            align-items: center;
            gap: 24px;
        }

        .nav-right {
            display: flex;
            align-items: center;
            gap: 24px;
        }

        /* 3D Visual Placeholder */
        .visual-3d {
            width: 400px;
            height: 400px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            font-weight: 600;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            transform: perspective(1000px) rotateY(-5deg) rotateX(5deg);
            transition: transform 0.3s ease;
        }

        .visual-3d:hover {
            transform: perspective(1000px) rotateY(-2deg) rotateX(2deg);
        }

        /* Stack Icon */
        .stack-icon {
            display: inline-block;
            width: 1em;
            height: 1em;
            margin: 0 0.2em;
            vertical-align: baseline;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .headline { font-size: 48px; }
            .text-column { padding: 48px 0 0 48px; }
            .visual-3d { width: 300px; height: 300px; }
        }

        @media (max-width: 768px) {
            .two-column-layout { flex-direction: column; }
            .text-column { flex: none; padding: 32px; }
            .visual-column { flex: none; height: 400px; }
            .headline { font-size: 36px; }
            .device-frame { padding: 16px; }
            .content-panel { border-radius: 40px; }
            .inner-panel { border-radius: 24px; }
        }

        /* View Management */
        .view { display: none; }
        .view.active { display: block; }

        /* Legacy styles for existing functionality */
        .sat-test-option { border: 2px solid var(--border-light); transition: all 0.2s ease-in-out; border-radius: 12px; }
        .sat-test-option:hover { border-color: var(--accent-primary); background-color: #f8f9fa; }
        .sat-test-option.selected { border-color: var(--accent-primary); background-color: #eff6ff; }
        .sat-test-option.correct { border-color: #10b981; background-color: #d1fae5; }
        .sat-test-option.incorrect { border-color: #ef4444; background-color: #fee2e2; }
        .progress-bar-fill { transition: width 0.5s ease-in-out; background-color: var(--accent-primary); }
        .loader { width: 52px; height: 52px; border: 5px solid rgba(255, 255, 255, 0.2); border-bottom-color: var(--accent-primary); border-radius: 50%; display: inline-block; box-sizing: border-box; animation: rotation 1s linear infinite; }
        @keyframes rotation { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
    </style>
</head>
<body>
    <div id="app">
        <!-- Loading Screen -->
        <div id="loading-screen" class="fixed inset-0 bg-gray-900 bg-opacity-95 flex-col z-50 flex justify-center items-center text-white text-center p-4 transition-opacity duration-500">
            <div class="loader mb-6"></div>
            <h2 class="text-2xl font-bold mb-2 text-gray-100">Initializing DecA(I)de...</h2>
            <p id="loading-text" class="text-lg text-gray-300 w-full max-w-md"></p>
        </div>

        <!-- Main App Content -->
        <div id="app-content" class="hidden opacity-0 transition-opacity duration-500">

            <!-- Dashboard View -->
            <div id="view-dashboard" class="view active">
                <div class="device-frame">
                    <div class="content-panel">
                        <div class="inner-panel">
                            <!-- Header Navigation -->
                            <header class="header-nav">
                                <div class="nav-left">
                                    <div class="logo-text">DecA(I)de</div>
                                    <div class="nav-pill">
                                        <a href="#" class="nav-link nav-text" data-view="dashboard">Home</a>
                                        <a href="#" class="nav-link nav-text" data-view="study-guide">Study Guide</a>
                                        <a href="#" class="nav-link nav-text" data-view="practice-test-intro">Practice Test</a>
                                        <a href="#" class="nav-link nav-text">About</a>
                                    </div>
                                </div>
                                <div class="nav-right">
                                    <div class="language-dropdown">
                                        <span>EN</span>
                                        <svg width="12" height="8" viewBox="0 0 12 8" fill="none">
                                            <path d="M1 1L6 6L11 1" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                    </div>
                                    <button class="btn-primary">
                                        Let's Connect
                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                            <path d="M1 8H15M15 8L8 1M15 8L8 15" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                    </button>
                                </div>
                            </header>

                            <!-- Two Column Layout -->
                            <div class="two-column-layout">
                                <!-- Text Column -->
                                <div class="text-column">
                                    <h1 class="headline">
                                        There is a<br>
                                        Better
                                        <svg class="stack-icon" width="1em" height="1em" viewBox="0 0 24 24" fill="none">
                                            <rect x="3" y="3" width="18" height="4" rx="2" fill="currentColor" opacity="0.3"/>
                                            <rect x="3" y="10" width="18" height="4" rx="2" fill="currentColor" opacity="0.6"/>
                                            <rect x="3" y="17" width="18" height="4" rx="2" fill="currentColor"/>
                                        </svg>
                                        Way<br>
                                        to Secure.
                                    </h1>

                                    <a href="#" class="contact-link" style="margin-top: 24px;" data-view="practice-test-intro">
                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                            <path d="M3 8L13 8M13 8L8 3M13 8L8 13" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        Start Practice Test
                                    </a>

                                    <p class="body-text" style="margin-top: 8px;">
                                        Master the Digital SAT with our AI-powered practice system. Get personalized questions, detailed explanations, and track your progress toward your target score. Built with the latest College Board specifications for authentic test preparation.
                                    </p>
                                </div>

                                <!-- Visual Column -->
                                <div class="visual-column">
                                    <div class="visual-3d">
                                        <div style="text-align: center;">
                                            <div style="font-size: 48px; margin-bottom: 16px;">📚</div>
                                            <div>SAT Prep Engine</div>
                                            <div style="font-size: 14px; opacity: 0.8; margin-top: 8px;">AI-Powered Learning</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Footer Toggle -->
                            <div class="crazy-mode-toggle">
                                <span>Advanced Mode:</span>
                                <div class="toggle-switch" id="advanced-toggle">Off</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Study Guide View -->
            <div id="view-study-guide" class="view">
                <div class="device-frame">
                    <div class="content-panel">
                        <div class="inner-panel">
                            <!-- Header Navigation -->
                            <header class="header-nav">
                                <div class="nav-left">
                                    <div class="logo-text">DecA(I)de</div>
                                    <div class="nav-pill">
                                        <a href="#" class="nav-link nav-text" data-view="dashboard">Home</a>
                                        <a href="#" class="nav-link nav-text" data-view="study-guide" style="color: var(--accent-primary);">Study Guide</a>
                                        <a href="#" class="nav-link nav-text" data-view="practice-test-intro">Practice Test</a>
                                        <a href="#" class="nav-link nav-text">About</a>
                                    </div>
                                </div>
                                <div class="nav-right">
                                    <button class="btn-primary home-btn" data-view="dashboard">
                                        Return Home
                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                            <path d="M1 8H15M15 8L8 1M15 8L8 15" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                    </button>
                                </div>
                            </header>

                            <!-- Study Guide Layout -->
                            <div style="display: flex; height: calc(100% - 96px); margin-top: 96px;">
                                <!-- Sidebar -->
                                <aside id="sidebar" style="width: 320px; background: var(--bg-matte); border-radius: 24px; margin: 24px; padding: 32px; overflow-y: auto;">
                                    <h2 style="font-size: 24px; font-weight: 700; margin-bottom: 32px; color: var(--text-primary);">Study Guide</h2>
                                    <nav id="navigation-menu"></nav>
                                </aside>

                                <!-- Content Area -->
                                <main style="flex: 1; padding: 24px; overflow-y: auto;">
                                    <div id="content-area" style="max-width: 800px; margin: 0 auto; background: white; border-radius: 24px; padding: 48px; box-shadow: 0 4px 20px rgba(0,0,0,0.08);"></div>
                                </main>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Practice Test Intro View -->
            <div id="view-practice-test-intro" class="view">
                <div class="device-frame">
                    <div class="content-panel">
                        <div class="inner-panel">
                            <!-- Header Navigation -->
                            <header class="header-nav">
                                <div class="nav-left">
                                    <div class="logo-text">DecA(I)de</div>
                                    <div class="nav-pill">
                                        <a href="#" class="nav-link nav-text" data-view="dashboard">Home</a>
                                        <a href="#" class="nav-link nav-text" data-view="study-guide">Study Guide</a>
                                        <a href="#" class="nav-link nav-text" data-view="practice-test-intro" style="color: var(--accent-primary);">Practice Test</a>
                                        <a href="#" class="nav-link nav-text">About</a>
                                    </div>
                                </div>
                                <div class="nav-right">
                                    <button class="btn-primary home-btn" data-view="dashboard">
                                        Return Home
                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                            <path d="M1 8H15M15 8L8 1M15 8L8 15" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                    </button>
                                </div>
                            </header>

                            <!-- Centered Content -->
                            <div style="display: flex; align-items: center; justify-content: center; height: calc(100% - 96px); margin-top: 96px; text-align: center;">
                                <div style="max-width: 600px; padding: 48px;">
                                    <h1 class="headline" style="font-size: 48px; margin-bottom: 32px;">
                                        Reading & Writing<br>
                                        Practice Test
                                    </h1>

                                    <p class="body-text" style="font-size: 16px; line-height: 1.6; margin: 0 auto 48px; max-width: 480px;">
                                        This is a digital adaptive test with two modules. Your performance on Module 1 will determine the difficulty of Module 2. Each module contains 27 questions and takes 32 minutes to complete.
                                    </p>

                                    <button id="start-test-btn" class="btn-primary" style="padding: 16px 32px; font-size: 16px; border-radius: 32px;">
                                        Start Module 1
                                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                                            <path d="M4 10H16M16 10L10 4M16 10L10 16" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Practice Test View -->
            <div id="view-practice-test" class="view">
                <div class="device-frame">
                    <div class="content-panel">
                        <div class="inner-panel">
                            <!-- Test Header -->
                            <header style="padding: 24px 32px; border-bottom: 1px solid var(--border-light); display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <h2 id="test-section-title" style="font-size: 18px; font-weight: 600; color: var(--text-primary); margin: 0;"></h2>
                                </div>
                                <div style="display: flex; align-items: center; gap: 24px;">
                                    <div id="test-timer" style="background: var(--bg-matte); padding: 8px 16px; border-radius: 16px; font-weight: 600; font-size: 16px;">--:--</div>
                                </div>
                            </header>

                            <!-- Progress Bar -->
                            <div style="height: 4px; background: var(--border-light); margin: 0 32px;">
                                <div id="test-progress-bar-top" class="progress-bar-fill" style="height: 100%; border-radius: 2px;"></div>
                            </div>

                            <!-- Test Content -->
                            <div style="display: flex; height: calc(100% - 140px); gap: 24px; padding: 24px 32px;">
                                <!-- Passage Column -->
                                <div id="test-passage-container" style="flex: 1; background: white; border-radius: 24px; padding: 32px; overflow-y: auto; box-shadow: 0 4px 20px rgba(0,0,0,0.08);"></div>

                                <!-- Question Column -->
                                <div id="test-question-container" style="flex: 1; background: white; border-radius: 24px; padding: 32px; display: flex; flex-direction: column; box-shadow: 0 4px 20px rgba(0,0,0,0.08);"></div>
                            </div>

                            <!-- Test Footer -->
                            <footer style="padding: 24px 32px; border-top: 1px solid var(--border-light); display: flex; justify-content: space-between; align-items: center;">
                                <div id="question-counter" style="font-weight: 500; color: var(--text-secondary);"></div>
                                <button id="next-question-btn" class="btn-primary" disabled style="padding: 12px 32px;">
                                    Next Question
                                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                        <path d="M1 8H15M15 8L8 1M15 8L8 15" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </button>
                            </footer>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Score Report View -->
            <div id="view-score-report" class="view">
                <div class="device-frame">
                    <div class="content-panel">
                        <div class="inner-panel">
                            <!-- Header Navigation -->
                            <header class="header-nav">
                                <div class="nav-left">
                                    <div class="logo-text">DecA(I)de</div>
                                    <div class="nav-pill">
                                        <a href="#" class="nav-link nav-text" data-view="dashboard">Home</a>
                                        <a href="#" class="nav-link nav-text" data-view="study-guide">Study Guide</a>
                                        <a href="#" class="nav-link nav-text">Results</a>
                                        <a href="#" class="nav-link nav-text">About</a>
                                    </div>
                                </div>
                                <div class="nav-right">
                                    <button class="btn-primary home-btn" data-view="dashboard">
                                        Return Home
                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                            <path d="M1 8H15M15 8L8 1M15 8L8 15" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                    </button>
                                </div>
                            </header>

                            <!-- Score Report Content -->
                            <div style="display: flex; align-items: center; justify-content: center; height: calc(100% - 96px); margin-top: 96px; padding: 48px;">
                                <div style="background: white; border-radius: 32px; padding: 64px; max-width: 900px; width: 100%; box-shadow: 0 8px 40px rgba(0,0,0,0.12); text-align: center;">
                                    <h1 class="headline" style="font-size: 48px; margin-bottom: 16px;">Test Complete!</h1>
                                    <p style="color: var(--text-secondary); font-size: 16px; margin-bottom: 48px;">Here's your performance breakdown</p>

                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 48px; align-items: center; margin-bottom: 48px;">
                                        <!-- Score Display -->
                                        <div>
                                            <h3 style="color: var(--text-secondary); font-size: 16px; margin-bottom: 16px;">Reading & Writing Scaled Score</h3>
                                            <p id="scaled-score" style="font-size: 96px; font-weight: 700; color: var(--accent-primary); margin: 0; line-height: 1;">---</p>
                                            <div style="margin-top: 32px;">
                                                <p id="total-correct" style="font-size: 20px; font-weight: 600; color: var(--text-primary); margin: 8px 0;">--/-- Correct</p>
                                                <p id="module1-correct" style="font-size: 14px; color: var(--text-secondary); margin: 4px 0;">Module 1: --/27</p>
                                                <p id="module2-correct" style="font-size: 14px; color: var(--text-secondary); margin: 4px 0;">Module 2: --/27</p>
                                            </div>
                                        </div>

                                        <!-- Chart -->
                                        <div style="position: relative; height: 300px;">
                                            <canvas id="scoreChart"></canvas>
                                        </div>
                                    </div>

                                    <!-- Action Buttons -->
                                    <div style="display: flex; gap: 16px; justify-content: center;">
                                        <button id="review-answers-btn" class="btn-primary" style="padding: 16px 32px;">
                                            Review Answers
                                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                                <path d="M1 8H15M15 8L8 1M15 8L8 15" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </button>
                                        <button class="home-btn" data-view="dashboard" style="border: 1px solid var(--border-medium); background: white; color: var(--text-primary); border-radius: 24px; padding: 16px 32px; font-weight: 500; cursor: pointer; display: inline-flex; align-items: center; gap: 8px;">
                                            Return to Dashboard
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Review Test View -->
            <div id="view-review-test" class="view">
                <div class="device-frame">
                    <div class="content-panel">
                        <div class="inner-panel">
                            <!-- Review Header -->
                            <header style="padding: 24px 32px; border-bottom: 1px solid var(--border-light); display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <h2 style="font-size: 18px; font-weight: 600; color: var(--text-primary); margin: 0;">Reviewing Test</h2>
                                </div>
                                <div>
                                    <button class="btn-primary home-btn" data-view="dashboard">
                                        End Review
                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                            <path d="M1 8H15M15 8L8 1M15 8L8 15" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                    </button>
                                </div>
                            </header>

                            <!-- Progress Bar -->
                            <div style="height: 4px; background: var(--border-light); margin: 0 32px;">
                                <div id="review-progress-bar" class="progress-bar-fill" style="height: 100%; border-radius: 2px;"></div>
                            </div>

                            <!-- Review Content -->
                            <div style="display: flex; height: calc(100% - 140px); gap: 24px; padding: 24px 32px;">
                                <!-- Passage Column -->
                                <div id="review-passage-container" style="flex: 1; background: white; border-radius: 24px; padding: 32px; overflow-y: auto; box-shadow: 0 4px 20px rgba(0,0,0,0.08);"></div>

                                <!-- Question Column -->
                                <div id="review-question-container" style="flex: 1; background: white; border-radius: 24px; padding: 32px; display: flex; flex-direction: column; box-shadow: 0 4px 20px rgba(0,0,0,0.08);"></div>
                            </div>

                            <!-- Review Footer -->
                            <footer style="padding: 24px 32px; border-top: 1px solid var(--border-light); display: flex; justify-content: space-between; align-items: center;">
                                <button id="prev-review-btn" style="border: 1px solid var(--border-medium); background: white; color: var(--text-primary); border-radius: 24px; padding: 12px 24px; font-weight: 500; cursor: pointer; display: inline-flex; align-items: center; gap: 8px;" disabled>
                                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                        <path d="M15 8H1M1 8L8 15M1 8L8 1" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                    Previous
                                </button>

                                <div id="review-question-counter" style="font-weight: 500; color: var(--text-secondary);"></div>

                                <button id="next-review-btn" style="border: 1px solid var(--border-medium); background: white; color: var(--text-primary); border-radius: 24px; padding: 12px 24px; font-weight: 500; cursor: pointer; display: inline-flex; align-items: center; gap: 8px;">
                                    Next
                                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                        <path d="M1 8H15M15 8L8 1M15 8L8 15" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </button>
                            </footer>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        const guideData = [
            {
                part: "Part 1: The Foundations of Sentence Structure",
                rules: [
                    { id: 'rule1', title: '1. Sentence vs. Fragment', content: { cheatCode: 'A sentence must have a subject (a "who" or "what") and a main verb (a "did what"). If it\'s missing either, it\'s a fragment.', layman: 'Think of it as the "who did what" test. Every complete thought must name a subject and state what that subject is or does. "The dog barked" passes the test. "The dog barking loudly" fails, because "barking" isn\'t a main verb—it doesn\'t state a complete action. "Because the dog barked" also fails, because it\'s an incomplete thought that leaves you hanging.', nuance: 'The SAT creates fragments in two predictable ways:<br>1. <strong>Dependent Clauses:</strong> A clause that starts with a subordinating word (like `although`, `because`, `since`, `which`, `who`) is always a fragment, even though it has a subject and verb. It cannot stand alone.<br>2. <strong>-ING Phrases:</strong> An `-ing` word (a participle) is a descriptive word, not a main verb. A phrase built around an `-ing` word is always a fragment unless it\'s attached to a complete sentence.', examples: [{ question: "Which of the following is a complete sentence?", options: ["The scientist, who made a major discovery.", "The scientist made a major discovery."], correct: 1, explanation: 'The first option is a fragment because the word "who" creates a dependent clause. The second option is a complete sentence with a subject ("scientist") and a main verb ("made").' }, { question: "Which of the following is a fragment?", options: ["The car drives down the street.", "The car driving down the street."], correct: 1, explanation: 'The second option is a fragment because "driving" is a participle (a descriptive word), not a main verb. The first option is a complete sentence.' }] } },
                    { id: 'rule2', title: '2. Connecting Complete Sentences', content: { cheatCode: 'To connect two complete sentences, you must use one of three things: a period, a semicolon, or a comma + a FANBOYS conjunction.', layman: 'Think of two separate train cars (two complete sentences). To connect them properly, you have three options:<br>1. <strong>Period (.):</strong> Leave a clear space between them. `She left. He stayed.`<br>2. <strong>Semicolon (;):</strong> Use a strong, direct metal coupler. `She left; he stayed.`<br>3. <strong>Comma + FANBOYS (, and/but/so...):</strong> Use a weaker coupler that requires a pin to hold it together. `She left, but he stayed.`', nuance: 'This creates the most powerful strategic shortcut on the test, the <strong>"Period Test."</strong> Because a period, a semicolon, and a comma + `and`/`but` are all grammatically identical ways to separate two sentences, they can\'t all be options if only one answer is correct.<br><strong>Strategy:</strong> If you see answer choices that include `sentence. New sentence`, `sentence; new sentence`, and `sentence, and new sentence`, you can eliminate all of them at once. The correct answer must be something else.', examples: [{ question: "Which sentence correctly connects two complete thoughts?", options: ["The experiment was a success, the results were published.", "The experiment was a success; the results were published."], correct: 1, explanation: 'The first option is a comma splice. A comma alone cannot connect two complete sentences. The second option correctly uses a semicolon.' }] } },
                    { id: 'rule3', title: '3. The Comma Splice', content: { cheatCode: 'A comma by itself is too weak to connect two complete sentences.', layman: 'You can\'t use a single piece of tape to hold two heavy boxes together—it will break in the middle. The comma is that weak piece of tape, and the complete sentences are the heavy boxes. Using only a comma to connect them creates a "comma splice."', nuance: 'The SAT loves comma splices. The most common trap format is: <strong>`...complete sentence, pronoun...`</strong> (e.g., "...the system failed, *it* needed to be redesigned."). When you see this, you know you must fix it in one of four ways:<br>1. Change the comma to a period.<br>2. Change the comma to a semicolon.<br>3. Add a FANBOYS conjunction after the comma.<br>4. Make one clause dependent (subordinate).', examples: [{ question: "Identify the comma splice.", options: ["The artist was a visionary, for she was ahead of her time.", "The artist was a visionary, she was ahead of her time."], correct: 1, explanation: 'The second sentence is a comma splice because it uses only a comma to connect two complete sentences. The first sentence correctly uses a comma followed by a FANBOYS conjunction ("for").' }] } }
                ]
            },
            {
                part: "Part 2: Mastering Punctuation",
                rules: [
                    { id: 'rule4', title: '4. The Semicolon (;)', content: { cheatCode: 'A semicolon is a "super-period" that connects two *closely related* complete sentences.', layman: 'Use a semicolon only where you could also use a period. The semicolon just adds a bit of style, suggesting the two sentences are linked thematically. If you can\'t swap the semicolon for a period, it\'s wrong.', nuance: 'The SAT tests this in two ways:<br>1. <strong>Semicolon vs. Comma:</strong> The most common error is using a semicolon to connect a complete sentence to a fragment. Remember: both sides of a semicolon must be complete sentences.<br>2. <strong>Semicolon with Transitions:</strong> A semicolon is used *before* transition words like `however`, `therefore`, and `consequently`. A comma comes *after* them.', examples: [{ question: "Which sentence uses the semicolon correctly?", options: ["The sun set; revealing the stars.", "The sun set; the stars emerged."], correct: 1, explanation: 'The first option is incorrect because "revealing the stars" is a fragment. The second option is correct because both clauses are complete sentences.' }] } },
                    { id: 'rule5', title: '5. The Colon (:)', content: { cheatCode: 'A colon must follow a complete sentence and introduces a list, explanation, or quotation.', layman: 'A colon acts like a gateway. The rule is that the statement *before* the gateway must be a complete sentence that could end with a period. What comes *after* the gateway can be a list, a clarification, or an example. Think of the colon as meaning "and here\'s what I mean."', nuance: 'The SAT sets traps by placing colons after fragments. The most common trap is using a colon after phrases like "such as" or "including."<br><strong>Strategy:</strong> To check if a colon is correct, cover up everything after it and read what comes before. If it\'s a complete, standalone sentence, the colon is likely correct.', examples: [{ question: "Which sentence uses the colon correctly?", options: ["The recipe included ingredients such as: flour, sugar, and eggs.", "The recipe included three main ingredients: flour, sugar, and eggs."], correct: 1, explanation: 'The first option is incorrect because "The recipe included ingredients such as" is not a complete sentence. The second option is correct because the clause before the colon is a complete sentence.' }] } },
                    { id: 'rule6', title: '6. Dashes & Parentheses', content: { cheatCode: 'Dashes, commas, and parentheses can all set off extra, non-essential information, but you can never mix and match them.', layman: 'Think of these as written "air quotes" or a way to whisper an aside to the reader. The rule is simple: if you open an interruption with a dash, you must close it with a dash. If you open with a comma, close with a comma.', nuance: 'The most common error is mixing punctuation (e.g., opening with a dash and closing with a comma). The second is using only one comma or one dash when a pair is needed to close off the interruption.<br><strong>Strategy:</strong> To check if a phrase is truly non-essential, mentally (or physically) cross out the information between the two punctuation marks. The remaining sentence should still be complete and grammatically sound.', examples: [{ question: "Which sentence is punctuated correctly?", options: ["My brother—a talented musician, is in a band.", "My brother—a talented musician—is in a band."], correct: 1, explanation: 'The first option incorrectly mixes a dash and a comma. The second option correctly uses a pair of dashes to set off the non-essential information.' }] } },
                    { id: 'rule7', title: '7. Apostrophes', content: { cheatCode: 'An apostrophe shows possession (`the student\'s book`) or creates a contraction (`it\'s` for `it is`). Pronouns showing possession (like `its`, `your`, `their`) NEVER have apostrophes.', layman: '<strong>Possession:</strong> To show a noun owns something, add `\'s`. If the noun is plural and already ends in `s`, just add the apostrophe after it (e.g., `the students\' books`).<br><strong>Contraction:</strong> `It\'s`, `you\'re`, `they\'re` are just short ways of saying `it is`, `you are`, and `they are`.', nuance: 'This is one of the most frequently tested concepts. The primary trap is confusing `its` (possession) and `it\'s` (contraction), and `their`/`there`/`they\'re`.<br><strong>Strategy:</strong> When you see `it\'s`, read it as "it is." If the sentence still makes sense, it\'s correct. If not, the answer must be `its`.', examples: [{ question: "Which sentence is correct?", options: ["The dog wagged it's tail.", "The dog wagged its tail."], correct: 1, explanation: 'The first option is incorrect because "it\'s" means "it is." "The dog wagged it is tail" makes no sense. The second option correctly uses the possessive pronoun "its."' }] } },
                    { id: 'rule8', title: '8. Question Marks', content: { cheatCode: 'Use a question mark only for a direct question, not for a statement that describes or reports a question.', layman: 'If the sentence is literally asking something that requires an answer, use a question mark. If the sentence is just *telling you* about a question someone asked or wondered about, end it with a period.', nuance: 'The SAT sets this trap by embedding a question within a larger statement. These often involve words like `whether`, `if`, or `what`. Students see a question being discussed and incorrectly assume a question mark is needed.', examples: [{ question: "Which sentence is punctuated correctly?", options: ["I don't know what the answer is?", "I don't know what the answer is."], correct: 1, explanation: 'The first option is incorrect because it is an indirect question (a statement about a question). It should end with a period, as shown in the second option.' }] } }
                ]
            },
            {
                part: "Part 3: Mastering Core Grammar",
                rules: [
                    { id: 'rule9', title: '9. Subject-Verb Agreement', content: { cheatCode: 'Singular subjects get verbs that end in `-s` (The girl *walks*). Plural subjects get verbs that do not end in `-s` (The girls *walk*).', layman: 'This is the most fundamental rule of grammar, but it\'s the opposite of how nouns work, which can be confusing. For nouns, you add `-s` to make them plural. For verbs, you add `-s` to make them *singular* in the present tense.', nuance: 'The SAT\'s #1 trick is to hide the subject by inserting extra information between it and the verb.<br><strong>Strategy: Cross It Out.</strong> Physically cross out prepositional phrases (starting with `of`, `in`, `for`, etc.) and non-essential clauses that separate the subject from the verb. What\'s left is the core sentence.', examples: [{ question: "Which sentence has correct subject-verb agreement?", options: ["The quality of the recordings are not very good.", "The quality of the recordings is not very good."], correct: 1, explanation: 'The subject is "quality" (singular), not "recordings." Therefore, the singular verb "is" is required.' }] } },
                    { id: 'rule10', title: '10. Advanced S-V Agreement', content: { cheatCode: '`Each`, `every`, and `anyone` are singular. `There is` is for singular nouns; `There are` is for plural nouns. In sentences with reversed order, find the subject *after* the verb.', layman: 'This rule covers the specific tricky subjects the SAT uses to hide agreement errors.', nuance: '<strong>Indefinite Pronouns:</strong> Words like `each`, `every`, `anyone`, `everyone` are always grammatically singular.<br><strong>Collective Nouns:</strong> Nouns like `team`, `committee`, `group` are treated as singular.<br><strong>The "One of the..." Trap:</strong> In the phrase "one of the [PLURAL NOUNS] that/who [VERB]," the verb agrees with the plural noun right before it, not with `one`.', examples: [{ question: "Which sentence is correct?", options: ["Each of the students have a textbook.", "Each of the students has a textbook."], correct: 1, explanation: '"Each" is a singular indefinite pronoun, so it requires the singular verb "has."' }, { question: "Which sentence is correct?", options: ["She is one of the scientists who believe in the theory.", "She is one of the scientists who believes in the theory."], correct: 0, explanation: 'In this structure, "who" refers to "scientists" (plural), not "one." Therefore, the plural verb "believe" is correct.' }] } },
                    { id: 'rule11', title: '11. Pronoun Agreement & Clarity', content: { cheatCode: 'A pronoun (`it`, `they`, `he`, `which`) must clearly and logically refer to a specific noun (its antecedent) from earlier in the sentence.', layman: 'Pronouns are shortcuts. It must be 100% clear who or what the pronoun is replacing. If it could refer to two different things, it\'s wrong.', nuance: 'The SAT tests this in three main ways:<br>1. <strong>Number Agreement:</strong> Singular noun -> singular pronoun; Plural noun -> plural pronoun.<br>2. <strong>Ambiguity:</strong> A pronoun is wrong if it could refer to more than one noun.<br>3. <strong>Missing Antecedent:</strong> A pronoun is wrong if the noun it\'s supposed to refer to doesn\'t actually appear in the text.', examples: [{ question: "Which sentence has a pronoun error?", options: ["When the researcher met with the lab assistant, the researcher was nervous.", "When the researcher met with the lab assistant, he was nervous."], correct: 1, explanation: 'The second sentence has an ambiguous pronoun. Who is "he"? The researcher or the assistant? The first sentence clarifies this by repeating the noun.' }] } },
                    { id: 'rule12', title: '12. Pronoun Case: I vs. Me, Who vs. Whom', content: { cheatCode: 'Use subject pronouns (`I`, `he`, `she`, `who`) for the doer of the action. Use object pronouns (`me`, `him`, `her`, `whom`) for the receiver of the action or after prepositions.', layman: 'This is about choosing the right form of a pronoun. "Me went to the store" sounds wrong because "Me" is a receiver, not a doer. `Who` is a doer like `he`; `whom` is a receiver like `him`.', nuance: 'Use these two simple tricks:<br>1. <strong>Compound Objects:</strong> To decide between `I` and `me`, mentally remove the other person from the sentence.<br>2. <strong>Who vs. Whom (The "He/Him" Test):</strong> Substitute `he` or `him`. If `he` fits, use `who`. If `him` fits, use `whom`.', examples: [{ question: "Which sentence is correct?", options: ["The invitation was sent to my friend and I.", "The invitation was sent to my friend and me."], correct: 1, explanation: 'Remove "my friend and." The sentence becomes "The invitation was sent to me." Therefore, "me" is the correct object pronoun.' }, { question: "Which sentence is correct?", options: ["The award was given to the artist, who the critics praised.", "The award was given to the artist, whom the critics praised."], correct: 1, explanation: 'Test with he/him: "The critics praised him." Since "him" fits, the object pronoun "whom" is correct.' }] } },
                    { id: 'rule13', title: '13. Relative Pronouns', content: { cheatCode: 'Use `who` for people and `which` for things. Use `where` only for physical places and `when` only for times.', layman: 'These are "pointing" words that introduce a description. You have to use the right pointer for the right kind of noun.', nuance: '<strong>`Where` vs. `In Which` (800-Level Trap):</strong> `Where` must refer to a physical, geographical location. For non-physical places—such as a book, a study, a field of research, a time period, or a situation—you must use `in which`.', examples: [{ question: "Which sentence is correct?", options: ["It was a story where the hero saves the day.", "It was a story in which the hero saves the day."], correct: 1, explanation: 'A "story" is not a physical place, so "where" is incorrect. The correct construction is "in which."' }] } },
                    { id: 'rule14', title: '14. Verb Tense & Sequence', content: { cheatCode: 'Keep verb tenses the same unless there is a clear word indicating a time shift. To show one past event happened *before* another past event, use the past perfect (`had done`).', layman: 'Don\'t time travel in your sentences without a reason. If you start a story in the past tense, you should continue in the past tense.', nuance: '<strong>Past Perfect (`had done`):</strong> Marks the *earlier* of two actions that are *both* in the past. If you see `had done`, look for another simple past verb in the sentence. The "had" action always happens first.', examples: [{ question: "Which sentence correctly shows the sequence of past events?", options: ["By the time the police arrived, the thief already escaped.", "By the time the police arrived, the thief had already escaped."], correct: 1, explanation: 'The thief escaping happened *before* the police arrived. Both events are in the past. The past perfect "had escaped" correctly marks the earlier event.' }] } },
                    { id: 'rule15', title: '15. Parallel Structure', content: { cheatCode: 'Items in a list or comparison must be in the same grammatical format.', layman: 'Keep your patterns consistent. If you are listing actions, make them all match. Don\'t say you like `running`, `swimming`, and `to ride` a bike. Say you like `running`, `swimming`, and `riding`.', nuance: 'The SAT tests this with lists and two-part comparisons (`not only...but also`, `either...or`). The grammatical structure of the phrase following the first part must match the structure of the phrase following the second part.', examples: [{ question: "Which sentence has correct parallel structure?", options: ["The plan was designed not only to improve efficiency but also for reducing costs.", "The plan was designed not only to improve efficiency but also to reduce costs."], correct: 1, explanation: 'The first option incorrectly pairs an infinitive ("to improve") with a prepositional phrase ("for reducing"). The second option correctly pairs two infinitives ("to improve" and "to reduce").' }] } }
                ]
            },
            {
                part: "Part 4: Mastering Writing Strategy & Style",
                rules: [
                    { id: 'rule16', title: '16. Concision: Shorter is Better', content: { cheatCode: 'If multiple answer choices are grammatically correct and express the same information, the shortest one is almost always the right answer.', layman: 'The SAT hates wordiness. Don\'t use ten words when five will do. Your goal is to be as clear and direct as possible. Extra words that don\'t add new meaning are just clutter.', nuance: 'This principle specifically targets two common types of errors:<br>1. <strong>Redundancy:</strong> Saying the same thing twice (e.g., "annually each year").<br>2. <strong>Wordy & Passive Phrasing:</strong> The active voice (`The scientist conducted the experiment`) is better than the passive voice (`The experiment was conducted by the scientist`).', examples: [{ question: "Which sentence is the most concise?", options: ["The project was completed annually on a yearly basis.", "The project was completed annually."], correct: 1, explanation: '"Annually" and "yearly" mean the same thing. The second option correctly removes the redundant phrase.' }] } },
                    { id: 'rule17', title: '17. Modifiers: Placement is Everything', content: { cheatCode: 'A descriptive phrase must be placed directly next to the person or thing it is describing.', layman: 'Don\'t let your descriptions float away from what they\'re describing. If a phrase describes the dog, it needs to touch the word "dog." If you put it next to the word "owner," you\'re saying the owner has floppy ears and a tail.', nuance: 'The most tested format is the <strong>Dangling Modifier (Introductory Phrases)</strong>. A sentence will start with a descriptive phrase followed by a comma. The noun that comes immediately after the comma *must* be the thing being described.', examples: [{ question: "Which sentence has a dangling modifier?", options: ["Hoping to get a good grade, the student studied diligently for the exam.", "Hoping to get a good grade, the exam was studied for diligently by the student."], correct: 1, explanation: 'The second sentence is incorrect because the exam was not "hoping to get a good grade"; the student was. The subject "student" must immediately follow the introductory phrase.' }] } },
                    { id: 'rule18', title: '18. Logical Transitions', content: { cheatCode: 'Determine the logical relationship between two sentences *before* looking at the answer choices. Then, pick the transition word that matches.', layman: 'Transition words (`however`, `therefore`, `for example`) are the road signs of an essay. You can\'t just pick a sign because it looks nice; you have to install the one that points in the direction the sentences are actually going.', nuance: '<strong>Strategy: Cross-Out & Define.</strong> Physically cross out the transition word in the passage. Read the sentence before it and the sentence after it. Then, define their relationship in your own simple words ("They are opposites," or "The second one is an example of the first"). Finally, look at the answer choices and pick the word that fits your definition.', examples: [{ question: "Choose the best transition: The team practiced tirelessly for months. ______, they won the championship.", options: ["However", "Therefore"], correct: 1, explanation: 'Winning the championship is a *result* of practicing. Therefore, a cause-and-effect transition is needed.' }] } },
                    { id: 'rule19', title: '19. Relevance & Purpose', content: { cheatCode: 'A sentence is only relevant if it directly supports the specific topic of its paragraph. A detail about penguins doesn\'t belong in a paragraph about polar bears.', layman: 'Every paragraph has a single, main job. Every sentence must be a worker helping to get that job done. If a sentence is off-topic, it\'s a worker from a different factory who has wandered in by mistake—and you need to remove it.', nuance: 'For <strong>Adding/Deleting Sentences</strong> questions, state the paragraph\'s main point in your own words. Then, see if the sentence in question directly supports that point. For <strong>Placing Sentences</strong> questions, look for "pointer" words like "this discovery" or pronouns that refer to something in the previous sentence.', examples: [{ question: 'A paragraph describes the challenges of space travel. Should this sentence be added: "Many astronauts enjoy the view of Earth from space."', options: ["Yes, it adds an interesting detail.", "No, it blurs the paragraph's focus."], correct: 1, explanation: 'The paragraph\'s topic is the *challenges* of space travel. The astronauts\' enjoyment is off-topic and should not be added.' }] } }
                ]
            },
            {
                part: "Part 5: Mastering Diction and Comparisons",
                rules: [
                    { id: 'rule20', title: '20. Diction and Idioms', content: { cheatCode: 'Use words according to their precise dictionary definitions and be sure to use the correct prepositions that idiomatically follow certain verbs or adjectives.', layman: 'This is about choosing the right word for the job. You don\'t say you have an `affect` on something; you have an `effect`. English also has "word families"—words that always go with specific partners (e.g., `capable of`, `different from`).', nuance: 'The SAT frequently tests pairs like `affect/effect` (verb/noun), `than/then` (comparison/time), and `precede/proceed` (come before/go forward). Also, word choice must match the formal, academic tone of the passage (e.g., use "many" instead of "a lot of").', examples: [{ question: "Which sentence is correct?", options: ["The new law will have a strong affect on the economy.", "The new law will have a strong effect on the economy."], correct: 1, explanation: '"Effect" is a noun meaning "result" or "impact." "Affect" is a verb meaning "to influence." The sentence requires a noun.' }] } },
                    { id: 'rule21', title: '21. Logical Comparisons', content: { cheatCode: 'You must compare apples to apples, not apples to oranges. Compare actions to actions, people to people, and things to things.', layman: 'A sentence like "A cheetah\'s speed is faster than a turtle" is wrong. It\'s illogically comparing speed (a concept) to a turtle (an animal). You must compare a cheetah\'s speed to a turtle\'s speed.', nuance: 'Watch for comparisons between a person\'s work and the person themselves. To fix a faulty comparison, the SAT often uses the phrases `that of` (for singular comparisons) or `those of` (for plural comparisons).', examples: [{ question: "Which sentence makes a logical comparison?", options: ["The paintings of Monet are more famous than Van Gogh.", "The paintings of Monet are more famous than those of Van Gogh."], correct: 1, explanation: 'The first sentence illogically compares paintings to a person. The second correctly compares paintings to paintings by using "those of" as a substitute for "the paintings of."' }] } }
                ]
            },
            {
                part: "Part 6: Mastering the Reading Test",
                rules: [
                    { id: 'rule22', title: '22. Words in Context', content: { cheatCode: 'The correct answer is almost never the most common definition of the word. Cover the word, read the sentence, and find the answer choice that is the most logical and precise synonym for that specific context.', layman: 'Words have multiple personalities. The word `run` means something different in "run a race," "run a business," and "a run in your stockings." You must look at the specific job the word is doing in that exact sentence.', nuance: 'This is a test of precision. The wrong answers will often be more common definitions of the word that don\'t fit the passage\'s logic.<br><strong>Strategy: Treat it like a blank.</strong> Forget the original word for a moment. Read the sentence and determine what kind of word needs to fit in the blank to make logical sense. Then, test the answer choices.', examples: [{ question: 'In the sentence, "The scientist wanted to **address** the concerns of the committee," what does "address" most nearly mean?', options: ["Locate", "Speak about"], correct: 1, explanation: 'The scientist isn\'t looking for a location. They are going to speak about or deal with the concerns.' }] } },
                    { id: 'rule23', title: '23. Command of Evidence', content: { cheatCode: 'Answer the first question, then find the lines that serve as the direct, explicit proof for your answer. The correct evidence is the reason your first answer is true.', layman: 'The SAT asks, "What does the author think?" and then immediately follows with, "Prove it." You have to find the quotation that acts as the "smoking gun" for your first answer.', nuance: 'A common trap is an evidence choice that is on the same topic as your answer but doesn\'t *directly prove it*. <br><strong>Strategy 2 (Check evidence first):</strong> Look at the four evidence options in Q2 *first*. For each one, determine what claim it supports. Then, find the answer choice in Q1 that matches one of your summaries.', examples: [{ question: "If Q1 asks why lions are successful hunters, and the answer is 'cooperative strategies,' which is the best evidence for Q2?", options: ['"Lions are the only cats to live in social groups called prides."', '"By hunting in coordinated groups, prides are able to take down prey much larger than any single lion could handle alone."'], correct: 1, explanation: 'The second option provides direct proof of *how* cooperative strategies lead to success. The first option is related but doesn\'t explain their hunting success.' }] } },
                    { id: 'rule24', title: '24. Analyzing Purpose, Tone, and Perspective', content: { cheatCode: 'The author\'s purpose is *why* they wrote the passage. Their tone is their *attitude* toward the subject. Pay close attention to adjectives and adverbs to figure it out.', layman: 'You need to figure out the author\'s agenda and their "vibe."<br><strong>Purpose:</strong> Are they trying to inform, persuade, critique, entertain, or reflect?<br><strong>Tone:</strong> Are they excited (`thankfully`, `remarkable`), skeptical (`supposedly`, `so-called`), critical (`unfortunately`, `flawed`), or objective (neutral, factual language)?', nuance: 'The SAT asks you to infer the author\'s intent and attitude, which are revealed through specific word choices. Wrong answers will mischaracterize the author\'s stance (e.g., calling a critical passage "supportive").', examples: [{ question: 'What is the tone of this sentence: "The researcher\'s **supposed** breakthrough, which relied on a **shockingly small** sample size, has been met with **widespread doubt** from his peers."', options: ["Supportive and admiring", "Critical and skeptical"], correct: 1, explanation: 'The words "supposed," "shockingly small," and "widespread doubt" all indicate a negative and skeptical attitude towards the research.' }] } },
                    { id: 'rule25', title: '25. Reading Infographics (Charts & Graphs)', content: { cheatCode: 'First, read the title, axes (including units), and legend/key. Then, state the graph\'s main point or overall trend in one simple sentence before looking at the questions.', layman: 'Don\'t just glance at the picture. Take 15 seconds to understand what the graph is actually measuring. Is it showing change over time? A comparison between groups? Once you know its story, you can easily spot which answer choices are telling the truth about it.', nuance: 'Common traps include misreading the units, confusing correlation with causation, and ignoring the overall trend in favor of one specific data point.', examples: [{ question: 'A graph shows profits rising from 2010-2015, then falling sharply in 2016. What is the best interpretation?', options: ["The company's profits increased.", "The company's profits generally rose until a sharp decline in 2016."], correct: 1, explanation: 'The first option is an incomplete interpretation because it ignores the significant drop. The second option accurately describes the overall trend.' }] } },
                    { id: 'rule26', title: '26. Strategy for Paired Passages', content: { cheatCode: 'Read Passage 1 and summarize its main argument. Then read Passage 2, constantly asking, "How does this relate to Passage 1? Does it agree, disagree, or just discuss something different?"', layman: 'Think of it as listening to two people debate. You need to figure out their relationship. Are they on the same side but offering different evidence? Are they in direct opposition? Is Passage 2 critiquing the idea mentioned in Passage 1?', nuance: 'Most questions for this passage type will focus on the relationship between the two texts.<br><strong>Strategy: Map It Out.</strong> After reading each passage, jot down a 3-5 word summary of the author\'s main claim (e.g., "P1: Pro-nuclear energy"; "P2: Nuclear energy unsafe"). Then, note the relationship (e.g., "P2 directly disagrees with P1").', examples: [{ question: "P1 argues Shakespeare's plays were written by Bacon. P2 provides evidence that Shakespeare of Stratford was the author. What is the relationship?", options: ["Passage 2 supports the theory in Passage 1.", "Passage 2 refutes the theory in Passage 1."], correct: 1, explanation: '"Refutes" means "disproves." Passage 2 presents evidence against the theory in Passage 1, so it refutes it.' }] } },
                    { id: 'rule27', title: '27. Advanced Reading Analysis', content: { cheatCode: 'A correct inference *must be true* based on the text. When analyzing an argument, identify the main claim and how the author uses specific evidence to support it.', layman: 'This rule covers the trickiest analytical skills.<br><strong>Inferences:</strong> An inference isn\'t a wild guess. It\'s a small, direct logical step. If the passage says, "The ground was wet," you can infer it rained. You cannot infer there was a thunderstorm.<br><strong>Arguments:</strong> Think like a detective. What is the author trying to prove (the claim)? What facts or stories are they using to prove it (the evidence)? How do they connect the two (the reasoning)?', nuance: '<strong>Supportable Inferences:</strong> The SAT will present trap answers that are plausible but not directly supported. Always ask, "Can I prove this *only* with the information given?"<br><strong>Distinguishing Voices:</strong> In passages that discuss multiple viewpoints (e.g., Scientist A vs. Scientist B), be careful to attribute claims to the correct person. Trap answers will often correctly state a viewpoint but attribute it to the wrong source.', examples: [{ question: 'If a passage states, "The subject showed a faster heart rate and increased perspiration when shown the color red," what can you infer?', options: ["The subject was scared of the color red.", "The color red elicited a physiological response in the subject."], correct: 1, explanation: 'You can only infer what is directly supported by the text. The text describes a physiological response, not a specific emotion like fear.' }] } }
                ]
            }
        ];

        // --- Global Application State --- //
        const appState = {
            currentView: 'dashboard',
            lastViewedRule: localStorage.getItem('lastViewedRule') || 'rule1',
            lastTestResult: JSON.parse(localStorage.getItem('lastTestResult')) || null,
            testState: {
                module: 1,
                currentQuestionIndex: 0,
                questions: [],
                userAnswers: [],
                module1Correct: 0,
                module2Correct: 0,
                timerInterval: null,
                timeRemaining: 32 * 60,
            }
        };

        const loadingTexts = [
            "Don't worry, a comma splice is just a sentence with commitment issues.",
            "Finding the main idea... it's not hiding, we promise.",
            "Sharpening our pencils and your wit.",
            "Calculating the velocity of a dangling modifier...",
            "Remember: 'Shorter is better' applies to answers, not your study breaks.",
            "Waking up the AI... it's not a morning person either.",
            "Polishing the parallel structures.",
            "Making sure our semicolons are more than just fancy commas."
        ];
        
        // --- Core App Logic & Initialization --- //
        document.addEventListener('DOMContentLoaded', () => {
            const appContent = document.getElementById('app-content');
            const loadingScreen = document.getElementById('loading-screen');
            const loadingTextEl = document.getElementById('loading-text');

            loadingTextEl.textContent = loadingTexts[Math.floor(Math.random() * loadingTexts.length)];
            
            setTimeout(() => {
                loadingScreen.style.opacity = '0';
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                    appContent.classList.remove('hidden');
                    appContent.style.opacity = '1';
                }, 500);
            }, 1500);

            initializeRouter();
            initializeStudyGuide();
            initializePracticeTest();
            updateDashboard();
        });

        function showView(viewId) {
            // Hide all views
            document.querySelectorAll('.view').forEach(view => {
                view.classList.remove('active');
                view.style.display = 'none';
            });

            // Show target view
            const targetView = document.getElementById(viewId);
            if (targetView) {
                targetView.style.display = 'block';
                targetView.classList.add('active');
            }

            appState.currentView = viewId.replace('view-', '');
            window.scrollTo(0, 0);
        }
        
        function updateDashboard() {
             if (appState.lastTestResult) {
                const dashboardContent = document.getElementById('dashboard-content');
                let scoreCard = dashboardContent.querySelector('#score-card');
                if (!scoreCard) {
                    scoreCard = document.createElement('div');
                    scoreCard.id = 'score-card';
                    scoreCard.className = 'md:col-span-2 bg-white p-6 rounded-lg shadow-lg border border-gray-200';
                    dashboardContent.appendChild(scoreCard);
                }
                scoreCard.innerHTML = `
                    <h3 class="text-2xl font-bold text-gray-700 mb-4 text-center">Your Last Test Result</h3>
                    <p class="text-6xl font-bold text-center text-[var(--primary-accent)]">${appState.lastTestResult.scaledScore}</p>
                    <p class="text-center text-gray-500 mt-2">${appState.lastTestResult.totalCorrect} / 54 Correct</p>
                `;
            }
        }

        function initializeRouter() {
            document.querySelectorAll('.nav-link, .start-btn, .home-btn').forEach(button => {
                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    const view = e.currentTarget.dataset.view || 'dashboard';
                    showView(`view-${view}`);
                });
            });

            // Advanced mode toggle
            const advancedToggle = document.getElementById('advanced-toggle');
            if (advancedToggle) {
                advancedToggle.addEventListener('click', () => {
                    const isOn = advancedToggle.textContent === 'On';
                    advancedToggle.textContent = isOn ? 'Off' : 'On';
                    advancedToggle.style.background = isOn ? 'white' : 'var(--accent-primary)';
                    advancedToggle.style.color = isOn ? 'var(--text-primary)' : 'white';
                });
            }
        }
        
        // --- Study Guide Logic --- //
        function initializeStudyGuide() {
            const navigationMenu = document.getElementById('navigation-menu');
            const contentArea = document.getElementById('content-area');
            const sidebar = document.getElementById('sidebar');
            const menuToggle = document.getElementById('menu-toggle');

            function renderNavigation() {
                navigationMenu.innerHTML = '';
                guideData.forEach((part) => {
                    const partDiv = document.createElement('div');
                    partDiv.className = 'mb-4';
                    const partTitle = document.createElement('h3');
                    partTitle.className = 'text-lg font-semibold text-gray-700 mb-2 cursor-pointer p-2 rounded-md hover:bg-gray-200';
                    partTitle.textContent = part.part;
                    partDiv.appendChild(partTitle);
                    const ruleList = document.createElement('ul');
                    ruleList.className = 'space-y-1 ml-2 hidden';
                    part.rules.forEach((rule) => {
                        const listItem = document.createElement('li');
                        const link = document.createElement('a');
                        link.href = '#';
                        link.textContent = rule.title;
                        link.className = 'block p-2 rounded-md text-sm text-[var(--text-secondary)] hover:bg-[var(--border-color)] sidebar-link';
                        link.dataset.id = rule.id;
                        link.addEventListener('click', (e) => {
                            e.preventDefault();
                            renderContent(rule);
                            document.querySelectorAll('.sidebar-link').forEach(l => l.classList.remove('active'));
                            link.classList.add('active');
                            appState.lastViewedRule = rule.id;
                            localStorage.setItem('lastViewedRule', rule.id);
                            if (window.innerWidth < 768) {
                                sidebar.classList.add('-translate-x-full');
                            }
                        });
                        listItem.appendChild(link);
                        ruleList.appendChild(listItem);
                    });
                    partDiv.appendChild(ruleList);
                    navigationMenu.appendChild(partDiv);
                    partTitle.addEventListener('click', () => {
                        ruleList.classList.toggle('hidden');
                    });
                });
            }

            function renderContent(rule) {
                const ruleData = rule.content;
                let examplesHtml = ruleData.examples.map((ex, index) => `
                    <div class="mt-6 bg-gray-50 p-4 rounded-lg border border-gray-200">
                        <p class="font-semibold mb-3">Interactive Example ${index + 1}:</p>
                        <p class="mb-4 text-gray-800">${ex.question}</p>
                        <div class="space-y-2" id="quiz-${rule.id}-${index}">
                            ${ex.options.map((opt, optIndex) => `
                                <button class="w-full text-left p-3 rounded-lg quiz-option bg-white" data-option="${optIndex}">
                                    <span class="font-bold mr-2">${String.fromCharCode(65 + optIndex)}</span> ${opt}
                                </button>
                            `).join('')}
                        </div>
                        <div id="feedback-${rule.id}-${index}" class="mt-3 text-sm font-medium p-3 rounded-md hidden"></div>
                    </div>
                `).join('');

                contentArea.innerHTML = `
                    <div class="bg-white p-6 md:p-8 rounded-xl shadow-md border border-gray-200">
                        <h2 class="text-3xl font-bold text-[var(--primary-accent)] mb-6">${rule.title}</h2>
                        <div class="space-y-8">
                            <div><h4 class="text-xl font-semibold text-gray-800 mb-2">Cheat-Code</h4><p class="p-4 bg-amber-50 rounded-md text-yellow-800 italic">${ruleData.cheatCode}</p></div>
                            <div><h4 class="text-xl font-semibold text-gray-800 mb-2">Layman's Explanation</h4><p class="text-[var(--text-secondary)] leading-relaxed">${ruleData.layman}</p></div>
                            <div><h4 class="text-xl font-semibold text-gray-800 mb-2">The Nuance & How It's Tested</h4><div class="text-[var(--text-secondary)] leading-relaxed">${ruleData.nuance}</div></div>
                            <div><h3 class="text-2xl font-bold text-gray-800 border-b-2 border-[var(--primary-accent)] pb-2 mb-4">Test Your Knowledge</h3>${examplesHtml}</div>
                        </div>
                    </div>`;
                
                ruleData.examples.forEach((ex, index) => {
                    const quizContainer = document.getElementById(`quiz-${rule.id}-${index}`);
                    quizContainer.addEventListener('click', (e) => {
                        const button = e.target.closest('.quiz-option');
                        if (button && !button.disabled) {
                            const selectedOption = parseInt(button.dataset.option);
                            const feedbackEl = document.getElementById(`feedback-${rule.id}-${index}`);
                            Array.from(quizContainer.children).forEach(child => { child.disabled = true; });
                            
                            feedbackEl.classList.remove('hidden');
                            if (selectedOption === ex.correct) {
                                button.classList.add('correct');
                                feedbackEl.innerHTML = `<span class="font-bold">Correct!</span> ${ex.explanation}`;
                                feedbackEl.className = 'mt-3 text-sm font-medium p-3 rounded-md bg-green-100 text-green-800';
                            } else {
                                button.classList.add('incorrect');
                                quizContainer.children[ex.correct].classList.add('correct');
                                feedbackEl.innerHTML = `<span class="font-bold">Not quite.</span> ${ex.explanation}`;
                                feedbackEl.className = 'mt-3 text-sm font-medium p-3 rounded-md bg-red-100 text-red-800';
                            }
                        }
                    });
                });
            }
            
            menuToggle.addEventListener('click', () => sidebar.classList.toggle('-translate-x-full'));
            renderNavigation();
            const lastViewedLink = document.querySelector(`.sidebar-link[data-id="${appState.lastViewedRule}"]`);
            if (lastViewedLink) {
                lastViewedLink.closest('ul').classList.remove('hidden');
                lastViewedLink.click();
            } else {
                 const firstLink = navigationMenu.querySelector('.sidebar-link');
                 if(firstLink) firstLink.click();
            }
        }

        // --- Practice Test Logic --- //
        function initializePracticeTest() {
            document.getElementById('start-test-btn').addEventListener('click', () => startTestModule(1));
            document.getElementById('next-question-btn').addEventListener('click', handleNextQuestion);
            document.getElementById('review-answers-btn').addEventListener('click', startReview);
            document.getElementById('prev-review-btn').addEventListener('click', () => navigateReview(-1));
            document.getElementById('next-review-btn').addEventListener('click', () => navigateReview(1));
        }

        async function startTestModule(moduleNumber) {
            const loadingScreen = document.getElementById('loading-screen');
            const loadingTextEl = document.getElementById('loading-text');
            showView('loading-screen');
            loadingScreen.style.display = 'flex';
            loadingScreen.style.opacity = '1';
            
            loadingTextEl.textContent = loadingTexts[Math.floor(Math.random() * loadingTexts.length)];

            appState.testState.module = moduleNumber;
            const startIndex = (moduleNumber - 1) * 27;
            appState.testState.currentQuestionIndex = startIndex;
            
            if (moduleNumber === 1) {
                appState.testState.questions = [];
                appState.testState.userAnswers = [];
            }

            let difficulty = 'medium';
            if (moduleNumber === 2) {
                const score = appState.testState.module1Correct;
                if (score > 18) difficulty = 'hard';
                else if (score < 10) difficulty = 'easy';
            }
            
            try {
                 const newQuestions = await generateTestModule(27, difficulty);
                 if (newQuestions.length < 27) throw new Error("AI failed to generate enough questions.");
                 appState.testState.questions.push(...newQuestions);

                loadingScreen.style.opacity = '0';
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                    showView('view-practice-test');
                    renderTestQuestion();
                    startTimer();
                }, 500);
            } catch (error) {
                 loadingScreen.innerHTML = `<div class="text-center"><h2 class="text-2xl font-bold mb-4 text-red-400">Generation Failed</h2><p class="text-gray-300">Could not generate the practice test. This might be due to an API key issue or network error.</p><button class="home-btn mt-6 bg-white text-gray-800 font-bold py-2 px-6 rounded-full">Return Home</button></div>`;
                 document.querySelector('#loading-screen .home-btn').addEventListener('click', () => {
                    loadingScreen.style.opacity = '0';
                    setTimeout(() => {
                        loadingScreen.style.display = 'none';
                        showView('view-dashboard');
                    }, 500);
                 });
            }
        }
        
        // SAT Question Generation System - College Board Specification Compliant
        const SAT_SPECS = {
            domains: {
                "Craft & Structure": {
                    percentage: 28,
                    skills: ["Words in Context", "Text Structure & Purpose", "Cross-Text Connections"]
                },
                "Information & Ideas": {
                    percentage: 26,
                    skills: ["Central Ideas & Details", "Inferences", "Command of Evidence - Textual", "Command of Evidence - Quantitative"]
                },
                "Standard English Conventions": {
                    percentage: 26,
                    skills: ["Boundaries", "Form, Structure, & Sense"]
                },
                "Expression of Ideas": {
                    percentage: 20,
                    skills: ["Transitions", "Rhetorical Synthesis"]
                }
            },
            subjectMix: {
                "science": 30,
                "history": 30,
                "arts": 20,
                "workplace": 10,
                "paired": 10
            },
            difficultyDistribution: {
                easy: 45,
                medium: 35,
                hard: 20
            }
        };

        function buildSatPrompt(skill, difficulty, domain) {
            const basePrompt = `Generate an authentic Digital SAT Reading & Writing question following College Board specifications exactly.

CRITICAL REQUIREMENTS:
- Skill: "${skill}" (Domain: "${domain}")
- Difficulty: "${difficulty}"
- Passage: 25-150 words, grade 11-12 complexity (Flesch-Kincaid)
- Mean sentence length: 17±3 words, include ≥1 complex sentence
- Tier-2 academic vocabulary: 3-5% of tokens
- Line numbering: restart at 1, show every 5th line
- Answer choices: differ by ≤2 words and ≤15 characters
- Response format: Raw JSON only, no markdown or explanations

JSON STRUCTURE:
{
  "passage": "text with line numbers every 5 lines",
  "question": "stem following College Board patterns",
  "options": ["A", "B", "C", "D"],
  "correctAnswerIndex": 0-3,
  "explanation": "brief justification",
  "lineNumbers": true
}`;

            let skillSpecific = "";

            switch(skill) {
                case "Words in Context":
                    skillSpecific = `
WORDS IN CONTEXT SPECIFICATIONS:
- Stem: "In line X, the word '[target]' most nearly means"
- Target word: 5-8 letters, secondary meaning (not primary dictionary sense)
- All choices: single words, same part of speech, alphabetical order
- Distractors: 3 other valid dictionary senses that don't fit context
- Difficulty scaling: Easy=primary sense, Medium=secondary sense, Hard=figurative sense`;
                    break;

                case "Central Ideas & Details":
                    skillSpecific = `
CENTRAL IDEAS SPECIFICATIONS:
- Stem: "Which choice best states the main purpose of the text?"
- Correct answer: paraphrase (not copy) of thesis, ≤12 words, 0 commas
- Distractors: (1) too narrow, (2) too broad, (3) off-topic
- Passage should have clear thesis statement`;
                    break;

                case "Transitions":
                    skillSpecific = `
TRANSITIONS SPECIFICATIONS:
- Stem: "Which choice completes the text with the most logical transition?"
- Mark blank with ‹...›
- Choices: 1 causal, 1 contrast, 1 additive, 1 synonym of correct category
- Correct answer must fit rhetorical relationship between sentences
- All choices ±1 word length`;
                    break;

                case "Command of Evidence - Textual":
                    skillSpecific = `
COMMAND OF EVIDENCE SPECIFICATIONS:
- Stem: "Which quotation from the text best supports [specific claim]?"
- Choices: 4 line ranges in ascending order (e.g., lines 3-4, lines 7-8, etc.)
- Correct span: ≤2 lines, contains direct proof of claim
- One distractor must overlap another by ≥1 line
- Include specific claim that needs evidence`;
                    break;

                case "Inferences":
                    skillSpecific = `
INFERENCE SPECIFICATIONS:
- Stem: "Which inference is best supported by the text?"
- Correct answer: requires exactly 1 logical leap (cause→effect, part→whole)
- Distractors: (1) opposite polarity, (2) too speculative, (3) true but not inferable
- Never test fact retrieval - must require reasoning`;
                    break;

                case "Text Structure & Purpose":
                    skillSpecific = `
TEXT STRUCTURE SPECIFICATIONS:
- Stem: "How does paragraph X mainly function in the text?"
- Correct answer: rhetorical role label ≤9 words
- Distractors: (1) too narrow, (2) too broad, (3) wrong relation
- Passage must have clear paragraph structure`;
                    break;

                case "Boundaries":
                    skillSpecific = `
BOUNDARIES SPECIFICATIONS:
- Test: run-ons, comma splices, semicolon/colon/dash errors
- Option A: reproduce original text (implicit NO CHANGE)
- Correct fix: minimal punctuation change only
- Distractors: 3 options each with different wrong punctuation
- Original sentence contains exactly one boundary error`;
                    break;

                case "Form, Structure, & Sense":
                    skillSpecific = `
FORM/STRUCTURE SPECIFICATIONS:
- Test: SV agreement, pronoun clarity, modifier placement, parallelism, verb tense
- Correct fix: ≤2 edits, preserve meaning
- Distractors: (1) original, (2) fixes but introduces new error, (3) meaning shift
- Original contains exactly one grammatical error`;
                    break;

                case "Rhetorical Synthesis":
                    skillSpecific = `
RHETORICAL SYNTHESIS SPECIFICATIONS:
- Include: student prompt ≤18 words + text source + simple chart/graph
- Stem: "Which finding would most strengthen the student's argument?"
- Correct answer: aligns exact noun + verb with prompt goal
- Distractors: (1) tangential, (2) opposite direction, (3) irrelevant statistic
- Chart: 3-6 data points, simple bar/line format`;
                    break;

                default:
                    skillSpecific = `- Follow standard College Board patterns for this skill type`;
            }

            const difficultyGuidance = {
                "easy": "- Use concrete topics, explicit details, include 1 obviously wrong distractor\n- Primary word meanings, straightforward syntax",
                "medium": "- Secondary word meanings, distractors share ≥2 key nouns\n- Embed subtle overlaps, require minor inference",
                "hard": "- Abstract topics, figurative language, reversal logic\n- Overlapping line ranges, fix multiple micro-errors in grammar items"
            };

            return basePrompt + skillSpecific + "\n\nDIFFICULTY GUIDANCE:\n" + difficultyGuidance[difficulty];
        }


        async function generateTestModule(numQuestions, difficulty) {
            // Calculate domain distribution based on College Board specifications
            const totalQuestions = 27;
            const domainCounts = {
                "Craft & Structure": Math.round(totalQuestions * 0.28), // ~7-8 questions
                "Information & Ideas": Math.round(totalQuestions * 0.26), // ~7 questions
                "Standard English Conventions": Math.round(totalQuestions * 0.26), // ~7 questions
                "Expression of Ideas": Math.round(totalQuestions * 0.20) // ~5-6 questions
            };

            // Adjust to ensure exactly 27 questions
            const totalAllocated = Object.values(domainCounts).reduce((a, b) => a + b, 0);
            if (totalAllocated !== totalQuestions) {
                domainCounts["Craft & Structure"] += (totalQuestions - totalAllocated);
            }

            // Create question blueprint following College Board domain order
            const questionBlueprint = [];

            // Domain order: C&S → I&I → SEC → EoI
            const domainOrder = ["Craft & Structure", "Information & Ideas", "Standard English Conventions", "Expression of Ideas"];

            domainOrder.forEach(domain => {
                const skills = SAT_SPECS.domains[domain].skills;
                const questionsForDomain = domainCounts[domain];

                // Distribute questions across skills within domain
                for (let i = 0; i < questionsForDomain; i++) {
                    const skill = skills[i % skills.length];
                    questionBlueprint.push({
                        domain: domain,
                        skill: skill,
                        difficulty: assignDifficulty(i, questionsForDomain, difficulty)
                    });
                }
            });

            // Generate questions following the blueprint
            const questions = [];
            const maxRetries = 3;

            for (let i = 0; i < questionBlueprint.length; i++) {
                const spec = questionBlueprint[i];
                let question = null;
                let retries = 0;

                while (!question && retries < maxRetries) {
                    try {
                        question = await generateSingleQuestion(spec.skill, spec.difficulty, spec.domain);
                        if (question && validateQuestion(question)) {
                            questions.push(question);
                        } else {
                            question = null;
                            retries++;
                        }
                    } catch (error) {
                        console.warn(`Failed to generate question ${i + 1}, attempt ${retries + 1}:`, error);
                        retries++;
                    }
                }

                // Fallback if generation fails
                if (!question) {
                    question = createFallbackQuestion(spec.skill, spec.difficulty, spec.domain);
                    questions.push(question);
                }
            }

            return questions;
        }

        function assignDifficulty(questionIndex, totalInDomain, moduleDifficulty) {
            // Distribute difficulty within domain: easy → medium → hard
            const position = questionIndex / totalInDomain;

            if (moduleDifficulty === 'easy') {
                if (position < 0.6) return 'easy';
                if (position < 0.9) return 'medium';
                return 'hard';
            } else if (moduleDifficulty === 'hard') {
                if (position < 0.2) return 'easy';
                if (position < 0.5) return 'medium';
                return 'hard';
            } else { // medium
                if (position < 0.45) return 'easy';
                if (position < 0.8) return 'medium';
                return 'hard';
            }
        }

        async function generateSingleQuestion(skill, difficulty, domain) {
            const prompt = buildSatPrompt(skill, difficulty, domain);

            try {
                // This would normally call an AI API like OpenAI GPT-4
                // For demo purposes, we'll return a mock question
                return createMockQuestion(skill, difficulty, domain);
            } catch (error) {
                console.error('Question generation failed:', error);
                return null;
            }
        }

        function validateQuestion(question) {
            if (!question || !question.passage || !question.question || !question.options) {
                return false;
            }

            // Validate passage length (25-150 words)
            const wordCount = question.passage.split(/\s+/).length;
            if (wordCount < 25 || wordCount > 150) {
                return false;
            }

            // Validate 4 options
            if (!Array.isArray(question.options) || question.options.length !== 4) {
                return false;
            }

            // Validate answer choice length parity (≤2 words, ≤15 chars difference)
            const lengths = question.options.map(opt => ({
                words: opt.split(/\s+/).length,
                chars: opt.length
            }));

            const maxWords = Math.max(...lengths.map(l => l.words));
            const minWords = Math.min(...lengths.map(l => l.words));
            const maxChars = Math.max(...lengths.map(l => l.chars));
            const minChars = Math.min(...lengths.map(l => l.chars));

            if (maxWords - minWords > 2 || maxChars - minChars > 15) {
                return false;
            }

            return true;
        }

        function createMockQuestion(skill, difficulty, domain) {
            // This creates realistic mock questions for demonstration
            // In production, this would be replaced with actual AI generation

            const mockQuestions = {
                "Words in Context": {
                    passage: "The scientist's research was groundbreaking, but her colleagues remained skeptical of the novel approach. Despite the initial resistance, her methodology eventually gained widespread acceptance in the academic community. The innovation represented a significant departure from traditional practices.",
                    question: "In line 2, the word 'novel' most nearly means",
                    options: ["fictional", "new", "lengthy", "published"],
                    correctAnswerIndex: 1,
                    explanation: "In this context, 'novel' means 'new' or 'innovative,' referring to the scientist's fresh approach to research."
                },
                "Central Ideas & Details": {
                    passage: "Urban gardens are transforming city landscapes across America. These green spaces provide fresh produce to local communities while reducing food transportation costs. Additionally, they create gathering places where neighbors can connect and children can learn about agriculture. The movement represents a return to sustainable, community-based food systems.",
                    question: "Which choice best states the main purpose of the text?",
                    options: [
                        "To explain how urban gardens reduce transportation costs",
                        "To describe the multiple benefits of urban gardens",
                        "To argue that children should learn about agriculture",
                        "To compare urban and rural farming methods"
                    ],
                    correctAnswerIndex: 1,
                    explanation: "The passage discusses several benefits of urban gardens: providing fresh produce, reducing costs, creating community spaces, and promoting sustainability."
                }
            };

            const baseQuestion = mockQuestions[skill] || mockQuestions["Words in Context"];

            // Adjust difficulty by modifying distractors
            if (difficulty === 'hard') {
                // Make distractors more subtle and overlapping
                if (skill === "Words in Context") {
                    baseQuestion.options = ["original", "innovative", "unprecedented", "unconventional"];
                }
            }

            return baseQuestion;
        }

        function createFallbackQuestion(skill, difficulty, domain) {
            return {
                passage: "This is a fallback question generated when the AI system is unavailable. The passage discusses a scientific discovery that changed our understanding of the natural world. Researchers spent years collecting data before reaching their conclusions.",
                question: "Which choice best describes the main topic of the passage?",
                options: [
                    "A scientific discovery and research process",
                    "The importance of data collection",
                    "Changes in scientific understanding",
                    "The work of dedicated researchers"
                ],
                correctAnswerIndex: 0,
                explanation: "The passage focuses on both the discovery and the research process that led to it."
            };
        }

        // Test Interface Functions
        function renderTestQuestion() {
            const currentQuestion = appState.testState.questions[appState.testState.currentQuestionIndex];
            if (!currentQuestion) return;

            const passageContainer = document.getElementById('test-passage-container');
            const questionContainer = document.getElementById('test-question-container');
            const progressBar = document.getElementById('test-progress-bar-top');
            const questionCounter = document.getElementById('question-counter');
            const sectionTitle = document.getElementById('test-section-title');

            // Update section title
            sectionTitle.textContent = `Module ${appState.testState.module} - Reading and Writing`;

            // Update progress
            const totalQuestions = appState.testState.module === 1 ? 27 : 54;
            const currentQuestionNumber = appState.testState.currentQuestionIndex + 1;
            const progress = (currentQuestionNumber / totalQuestions) * 100;
            progressBar.style.width = `${progress}%`;

            // Update question counter
            const moduleQuestionNumber = (appState.testState.currentQuestionIndex % 27) + 1;
            questionCounter.textContent = `Question ${moduleQuestionNumber} of 27`;

            // Render passage with line numbers
            let passageWithLines = currentQuestion.passage;
            if (currentQuestion.lineNumbers) {
                const lines = currentQuestion.passage.split('\n');
                passageWithLines = lines.map((line, index) => {
                    const lineNumber = index + 1;
                    const showNumber = lineNumber % 5 === 0 || lineNumber === 1;
                    return `<span class="line-number">${showNumber ? lineNumber : ''}</span>${line}`;
                }).join('\n');
            }

            passageContainer.innerHTML = `
                <div class="prose max-w-none">
                    <div class="whitespace-pre-line font-serif text-base leading-relaxed">${passageWithLines}</div>
                </div>
            `;

            // Render question and options
            questionContainer.innerHTML = `
                <div class="flex flex-col h-full">
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold mb-4">${currentQuestion.question}</h3>
                        <div class="space-y-3" id="test-options">
                            ${currentQuestion.options.map((option, index) => `
                                <button class="sat-test-option w-full text-left p-4 rounded-lg bg-white border-2" data-option="${index}">
                                    <span class="font-bold mr-3">${String.fromCharCode(65 + index)}</span>
                                    ${option}
                                </button>
                            `).join('')}
                        </div>
                    </div>
                </div>
            `;

            // Add click handlers for options
            document.getElementById('test-options').addEventListener('click', (e) => {
                const button = e.target.closest('.sat-test-option');
                if (button) {
                    // Clear previous selections
                    document.querySelectorAll('.sat-test-option').forEach(opt => {
                        opt.classList.remove('selected');
                    });

                    // Select clicked option
                    button.classList.add('selected');

                    // Store answer
                    const selectedOption = parseInt(button.dataset.option);
                    appState.testState.userAnswers[appState.testState.currentQuestionIndex] = selectedOption;

                    // Enable next button
                    document.getElementById('next-question-btn').disabled = false;
                }
            });

            // Restore previous selection if exists
            const previousAnswer = appState.testState.userAnswers[appState.testState.currentQuestionIndex];
            if (previousAnswer !== undefined) {
                const optionButton = document.querySelector(`[data-option="${previousAnswer}"]`);
                if (optionButton) {
                    optionButton.classList.add('selected');
                    document.getElementById('next-question-btn').disabled = false;
                }
            } else {
                document.getElementById('next-question-btn').disabled = true;
            }
        }

        function handleNextQuestion() {
            const currentIndex = appState.testState.currentQuestionIndex;
            const isModule1 = appState.testState.module === 1;
            const questionsInModule = 27;
            const moduleStartIndex = (appState.testState.module - 1) * questionsInModule;
            const questionInModule = currentIndex - moduleStartIndex + 1;

            if (questionInModule >= questionsInModule) {
                // End of module
                if (isModule1) {
                    // Calculate Module 1 score
                    appState.testState.module1Correct = calculateModuleScore(0, 26);
                    startTestModule(2);
                } else {
                    // End of test
                    finishTest();
                }
            } else {
                // Next question in current module
                appState.testState.currentQuestionIndex++;
                renderTestQuestion();
            }
        }

        function calculateModuleScore(startIndex, endIndex) {
            let correct = 0;
            for (let i = startIndex; i <= endIndex; i++) {
                const userAnswer = appState.testState.userAnswers[i];
                const correctAnswer = appState.testState.questions[i]?.correctAnswerIndex;
                if (userAnswer === correctAnswer) {
                    correct++;
                }
            }
            return correct;
        }

        function finishTest() {
            // Calculate final scores
            appState.testState.module1Correct = calculateModuleScore(0, 26);
            appState.testState.module2Correct = calculateModuleScore(27, 53);

            const totalCorrect = appState.testState.module1Correct + appState.testState.module2Correct;
            const scaledScore = calculateScaledScore(totalCorrect);

            // Store results
            const testResult = {
                totalCorrect: totalCorrect,
                module1Correct: appState.testState.module1Correct,
                module2Correct: appState.testState.module2Correct,
                scaledScore: scaledScore,
                date: new Date().toLocaleDateString()
            };

            appState.lastTestResult = testResult;
            localStorage.setItem('lastTestResult', JSON.stringify(testResult));

            // Stop timer
            if (appState.testState.timerInterval) {
                clearInterval(appState.testState.timerInterval);
            }

            // Show results
            showScoreReport();
        }

        function calculateScaledScore(totalCorrect) {
            // Simplified SAT scoring conversion (actual SAT uses more complex equating)
            const rawToScaled = {
                54: 800, 53: 790, 52: 780, 51: 770, 50: 760,
                49: 750, 48: 740, 47: 730, 46: 720, 45: 710,
                44: 700, 43: 690, 42: 680, 41: 670, 40: 660,
                39: 650, 38: 640, 37: 630, 36: 620, 35: 610,
                34: 600, 33: 590, 32: 580, 31: 570, 30: 560,
                29: 550, 28: 540, 27: 530, 26: 520, 25: 510,
                24: 500, 23: 490, 22: 480, 21: 470, 20: 460,
                19: 450, 18: 440, 17: 430, 16: 420, 15: 410,
                14: 400, 13: 390, 12: 380, 11: 370, 10: 360
            };

            return rawToScaled[totalCorrect] || (totalCorrect < 10 ? 200 + totalCorrect * 16 : 350);
        }

        function showScoreReport() {
            showView('view-score-report');

            // Update score display
            document.getElementById('scaled-score').textContent = appState.lastTestResult.scaledScore;
            document.getElementById('total-correct').textContent = `${appState.lastTestResult.totalCorrect}/54 Correct`;
            document.getElementById('module1-correct').textContent = `Module 1: ${appState.lastTestResult.module1Correct}/27`;
            document.getElementById('module2-correct').textContent = `Module 2: ${appState.lastTestResult.module2Correct}/27`;

            // Create performance chart
            createScoreChart();
        }

        function createScoreChart() {
            const ctx = document.getElementById('scoreChart').getContext('2d');

            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['Module 1', 'Module 2'],
                    datasets: [{
                        label: 'Correct Answers',
                        data: [appState.lastTestResult.module1Correct, appState.lastTestResult.module2Correct],
                        backgroundColor: ['#0d6efd', '#ffc107'],
                        borderColor: ['#0b5ed7', '#ffca2c'],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 27,
                            ticks: {
                                stepSize: 5
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        }

        function startTimer() {
            const timerDisplay = document.getElementById('test-timer');
            appState.testState.timeRemaining = 32 * 60; // 32 minutes in seconds

            appState.testState.timerInterval = setInterval(() => {
                appState.testState.timeRemaining--;

                const minutes = Math.floor(appState.testState.timeRemaining / 60);
                const seconds = appState.testState.timeRemaining % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;

                if (appState.testState.timeRemaining <= 0) {
                    clearInterval(appState.testState.timerInterval);
                    finishTest();
                }
            }, 1000);
        }

        // Review Mode Functions
        function startReview() {
            showView('view-review-test');
            appState.reviewIndex = 0;
            renderReviewQuestion();
        }

        function renderReviewQuestion() {
            const question = appState.testState.questions[appState.reviewIndex];
            const userAnswer = appState.testState.userAnswers[appState.reviewIndex];
            const correctAnswer = question.correctAnswerIndex;

            // Update progress
            const progress = ((appState.reviewIndex + 1) / appState.testState.questions.length) * 100;
            document.getElementById('review-progress-bar').style.width = `${progress}%`;

            // Update counter
            document.getElementById('review-question-counter').textContent = `Question ${appState.reviewIndex + 1} of ${appState.testState.questions.length}`;

            // Render passage
            document.getElementById('review-passage-container').innerHTML = `
                <div class="prose max-w-none">
                    <div class="whitespace-pre-line font-serif text-base leading-relaxed">${question.passage}</div>
                </div>
            `;

            // Render question with results
            document.getElementById('review-question-container').innerHTML = `
                <div class="flex flex-col h-full">
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold mb-4">${question.question}</h3>
                        <div class="space-y-3">
                            ${question.options.map((option, index) => {
                                let classes = 'w-full text-left p-4 rounded-lg border-2 ';
                                if (index === correctAnswer) {
                                    classes += 'sat-test-option correct';
                                } else if (index === userAnswer && userAnswer !== correctAnswer) {
                                    classes += 'sat-test-option incorrect';
                                } else {
                                    classes += 'sat-test-option';
                                }

                                return `
                                    <div class="${classes}">
                                        <span class="font-bold mr-3">${String.fromCharCode(65 + index)}</span>
                                        ${option}
                                        ${index === correctAnswer ? '<span class="ml-2 text-green-600 font-bold">✓ Correct</span>' : ''}
                                        ${index === userAnswer && userAnswer !== correctAnswer ? '<span class="ml-2 text-red-600 font-bold">✗ Your Answer</span>' : ''}
                                    </div>
                                `;
                            }).join('')}
                        </div>
                        <div class="mt-4 p-4 bg-blue-50 rounded-lg">
                            <h4 class="font-semibold text-blue-800 mb-2">Explanation:</h4>
                            <p class="text-blue-700">${question.explanation}</p>
                        </div>
                    </div>
                </div>
            `;

            // Update navigation buttons
            document.getElementById('prev-review-btn').disabled = appState.reviewIndex === 0;
            document.getElementById('next-review-btn').disabled = appState.reviewIndex === appState.testState.questions.length - 1;
        }

        function navigateReview(direction) {
            const newIndex = appState.reviewIndex + direction;
            if (newIndex >= 0 && newIndex < appState.testState.questions.length) {
                appState.reviewIndex = newIndex;
                renderReviewQuestion();
            }
        }
    </script>
</body>
</html>