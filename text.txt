Below is a full “build spec” for an AI-only item-generator that must recreate digital SAT Reading & Writing questions so faithfully that a human expert could not tell them from official College Board content. Everything is distilled from the public Assessment Framework v3.01 (Aug 2024) and from close reading of released practice forms.

1. Section architecture (must-match constants)
Attribute	Target value	Implementation notes
Modules per section	2 adaptive MST modules	
Scored items per section	54 (27 + 27)	
Time per module	32 min	
Average time per Q	71 s	
Stimulus/passage length	25–150 words (one discrete Q per stimulus) 
satsuite.collegeboard.org
satsuite.collegeboard.org
Graphics	≤ 30 % of items include a simple table, bar/line graph, chart, or pair of short passages	
MC format	4 options (A–D), single best answer; every form must keep answer-letter frequencies within ±5 % of uniform 
satsuite.collegeboard.org

2. Content-domain blueprint (operational distribution)
Domain	Sub-skills (skill/knowledge testing points)	% of a scored form	Q-count
Information & Ideas	Central Ideas & Details; Command of Evidence (textual + quantitative); Inferences	≈ 26 %	12–14
Craft & Structure	Words-in-Context; Text Structure & Purpose; Cross-Text Connections	≈ 28 %	13–15
Expression of Ideas	Rhetorical Synthesis; Transitions	≈ 20 %	8–12
Standard English Conventions	Boundaries; Form, Structure, & Sense	≈ 26 %	11–15

Generator rule: When assembling a 27-item module, sample domains in these proportions (±1 item) so that the full 54 Q form meets Table 10 exactly.

3. Text & language constraints
Dimension	Spec
Text complexity band	Each stimulus is leveled to grade-bands 9-11 or 12-14 (college-intro) using College Board’s syntactic–vocabulary model. Every form must include some 12-14 texts. 
satsuite.collegeboard.org
Vocabulary type	“Tier 2” academic words preferred; avoid esoterica unless testing WIC.
Sentence structure	Mean sentence length 15–22 words; ≥ 1 complex sentence per stimulus.
Subject-area mix (per 54 Q form)	≈ 30 % science, 30 % history/social studies, 20 % humanities/lit‐narrative, 10 % workplace/technology, 10 % paired/graphic.

4. Skill-by-skill templates (stem + distractor logic)
4.1 Information & Ideas
Sub-skill	Canonical stem	Correct answer design	Distractor patterns
Central Idea & Details	“Which choice best states the main purpose of the text?”	Paraphrase (not copy) of thesis; ≤ 12 words.	1 overly narrow, 1 overly broad, 1 off-topic.
Command of Evidence – Textual	“Which quotation from the text best supports the answer to the previous question?” (or “best supports the claim…”)	Quote spans ≤ 2 lines; shares ≥ 3 content words with target claim.	1 irrelevant but same topic, 1 only partially supportive, 1 extreme language.
Command of Evidence – Quantitative	Stimulus = 40–60 word blurb + simple graphic. Stem: “Which conclusion about X is best supported by the graphic?”	Precise numeric comparison.	Use numbers present but wrong inference.
Inferences	“Which inference is best supported by the text?”	Requires bridging 1 implicit step.	1 opposite-polarity, 1 too speculative, 1 true but not inferable.

4.2 Craft & Structure
Sub-skill	Stem snippet	Correct answer rules	Distractors
Words-in-Context	“In line __, the word X most nearly means —”	Same part of speech; 5–8 letters; context-appropriate sense.	3 other dictionary senses of X, ordered alphabetically.
Text Structure & Purpose	“How does paragraph 2 mainly function in the text?”	Labels rhetorical role (introduces example, contrasts claim…).	1 misidentifies role, 1 content trap, 1 unsupported extreme.
Cross-Text Connections	Paired 2×80-word passages. Stem: “Which choice identifies a claim both passages support?”	Claim must align with explicit or implicit agreement.	1 only P1, 1 only P2, 1 half-true generalization.

4.3 Expression of Ideas
Sub-skill	Stem	Correct answer	Distractors
Rhetorical Synthesis	Prompt + 2 sources (passage + graph). Stem: “Which finding if added to the graph would most strengthen the student’s argument?” OR “Which statement from Source 2 best supports …”	Pick evidence that directly matches prompt verb/noun pair.	1 tangential, 1 opposite direction, 1 irrelevant statistic.
Transitions	“Which choice completes the text with the most logical transition?” (blank marked ‹…›)	Fits rhetorical relationship (cause, contrast, continuation).	Provide the other two relationships + synonym of correct.

4.4 Standard English Conventions
Sub-skill	Error types to seed in stimulus	Correct fix	Distractor set
Boundaries	Run-ons, comma splices, fused clauses, incorrect semicolon/colon/dash.	Restore correct boundary punctuation and capitalization.	3 options each using different wrong punctuation.
Form, Structure, & Sense	SV agreement, pronoun clarity, modifier placement, parallelism.	Minimal, precise revision; keep ≤ 2 edits.	1 keeps original, 1 introduces new error, 1 fixes but adds new error.

5. Difficulty tuning knobs
Difficulty bucket	Item-level levers
Easy (1.0-1.9)	Explicit detail, familiar topic, distractors obviously off.
Medium (2.0-2.9)	Requires 1 inference or minor rewrite; distractors share wording overlap.
Hard (3.0-3.9)	Implicit reasoning, abstract topics, subtly flawed distractors; WIC uses secondary meaning; grammar item embeds multiple error types.

Keep overall easy : medium : hard ≈ 40 : 35 : 25 per form (College Board’s “adaptive targets”).

6. Answer-choice & layout conventions
Letter rotation: Aggregate key distribution A/B/C/D must stay within ±5 % of 25 %. Track counts as items are batched.

Length parity: In any one item, answer choices should differ by ≤ 2 words length and ≤ 15 characters to prevent clueing.

Typography: Use en dashes (–) for interruptions, em dashes for parentheticals; straight quotes, not smart quotes.

7. Passage sourcing rules
Fiction/literary narrative passages must be public-domain or custom-written pastiche; avoid recognizable plot lines.

Science stimuli should reference peer-reviewed findings in biology, chem, physics, or environmental science; include ≥ 1 numeric datum for potential quantitative-evidence items.

History/social studies should anchor in 19th–21st-century events; primary-source excerpts capped at 150 words.

No controversial topics (religion, partisan politics) unless written in neutral informative tone.

9. Assembly algorithm (module level)
Draw domain quotas (Table 10).

For each item slot:
a. Choose sub-skill template.
b. Generate or retrieve stimulus meeting subject-area & complexity spec.
c. Fill template → stem + 4 choices.
d. Tag with provisional difficulty; iterate distractor edits to hit target.

After 27 items, run global constraints (timing, answer rotation, topic diversity).

Repeat for second module with adaptive difficulty shift (if previous module “high performance,” skew difficulty bucket to ~20 % easy, 40 % medium, 40 % hard; else reverse).

elow is a bolt-on “granular spec” that layers every subtlety the College Board bakes into the digital-SAT Reading & Writing section on top of the high-level blueprint I gave you earlier. Feed these rules to your autonomous coding agent and it will hit the uncanny-valley of authenticity. (Numbered cross-refs show which requirement is enforced by the public Assessment Framework so you can anchor each rule in an official source.)

0 Core constants (do not deviate)
Constant	Exact value	Source
Items per section	54 scored, delivered in two 27-question modules	
satsuite.collegeboard.org
Stimulus length	25–150 words for every single text (≦ 200 in Bluebook character count)	
satsuite.collegeboard.org
Content-domain proportions	C&S ≈ 28 %, I&I ≈ 26 %, SEC ≈ 26 %, EoI ≈ 20 %	
satsuite.collegeboard.org
Domain order inside a module	C&S → I&I → SEC → EoI (both modules)	
satsuite.collegeboard.org
Line numbering	Restart at 1 for every stimulus; show every 5th line margin-number	(Framework p. 58, sample items)

1 Micro-sequence rules inside a module
Block grouping: Questions testing the same sub-skill appear back-to-back and are internally ordered easiest → hardest.

Evidence pairs:
Pattern: Inference Q immediately followed by Command-of-Evidence Q that cites 4 line-ranges, ascending order.
Line window: correct line span is ≤ 2 lines, always entirely contained inside the lines that justify the inference.

Adaptive targeting: In a high-difficulty second module, promote ≈ +10 % hard items by replacing the middle item of each block (item 14/27, 26/27, etc.) with a hard version of that sub-skill.

2 Passage-creation fingerprints
Dimension	Exact spec
Discipline mix (per 54 Q form)	30 % science, 30 % history/social science, 20 % arts/lit, 10 % workplace/tech, 10 % paired or graphic stimulus (counts toward prior bucket).
Text complexity bands	At least 4 stimuli in grades 12-14 band; rest evenly split 9-11 and one 6-8 “breather.”
Tone palette	Informative or neutral for non-fiction, measured narrative voice for fiction. Never humorous, sarcastic, or argumentative unless paired with rhetorical-analysis task.
Sentence architecture	Mean length 17 ± 3 words; ≥ 1 complex/compound-complex sentence; max 2 commas in any sentence < 25 words.
Tier-2 vocab density	Target 3–5 % of tokens; avoid Latinate > 4 syllables unless the skill is WIC.
Graphics	Bars or lines only; 3–6 plotted points/bars; axis labels ≤ 3 words each; legend title ≤ 5 words.

3 Sub-skill pattern book (the “secret sauce”)
A. Craft & Structure
Sub-skill	Stem skeleton	Distractor design heuristics
Words-in-Context (WIC)	“In line __, the word ‹target› most nearly means —”	All four choices single-word, same POS, 5–8 letters, alphabetical order. Wrong senses split 2 literal / 1 figurative.
Text Structure & Purpose	“How does paragraph 2 mainly function in the text?”	Three wrong roles: Too Narrow, Too Broad, Wrong Relation.
Cross-Text Connections	Two 80-word passages. Stem variants: “Which choice identifies a claim both passages support?” or “Which statement is true about how Passage 1 differs from Passage 2?”	Distractors pattern Only P1, Only P2, Half-True Both.

B. Information & Ideas
Sub-skill	Mandatory nuance
Central Ideas & Details	Correct paraphrase ≤ 12 words, 0 commas, no semicolons.
Command of Evidence—Textual	Line-range choices listed in ascending line order; one choice must overlap two others by ≥ 1 line to create lure.
Command of Evidence—Quantitative	Graphic always contains a reversal or crossover; correct answer states the direction of the relationship.
Inferences	Force one logical leap (e.g., cause→effect), never fact retrieval.

C. Expression of Ideas
Sub-skill	Fine points
Transitions	Blank marked ‹…›. Choice list: 1 causal, 1 contrast, 1 additive, 1 synonym of causal/contrast (acts as red herring). Correct answer’s semantic category must not repeat within previous 2 transition items.
Rhetorical Synthesis	Prompt = student goal (≤ 18 words). Two sources: 1 text, 1 chart. Correct choice aligns exact noun + verb with prompt goal; distractor nouns overlap ≤ 2 content words.

D. Standard English Conventions
Error family (SEC)	Allocation rule (per 54 Q)	Signature gimmicks
Boundaries	6–7 items	Option A reproduces original text (“implicit NO CHANGE”); at least one distractor mis-uses a semicolon; only one uses a dash pair; do not test comma-splice and SV agreement in the same item.
Form, Structure & Sense	6–7 items	Cycle through (a) parallelism, (b) misplaced modifier, (c) pronoun clarity, (d) verb tense, (e) SV agreement. Original sentence contains exactly one error.

4 Answer-choice engineering rules
Letter balance: Running tally per 54 Q form: 13 or 14 of each letter (± 1).

Length parity: Longest and shortest answer differ by ≤ 2 words and ≤ 15 characters.

Surface clues: Never allow unmatched parentheses, hyphenation, or capitalization to appear in only the correct answer.

Idiomatic traps: At most 1 distractor per form may hinge on US vs. UK usage (“among” vs. “amongst”).

5 Difficulty dial (how to “scale” an item)
Bucket	Manipulate…	Examples
Easy	Reduce inference distance; keep vocabulary concrete; use at least one blatantly wrong distractor (“Mars has an atmosphere of nitrogen and oxygen”).	
Medium	Embed subtle distractor overlap (share ≥ 2 key nouns); WIC uses secondary meaning still common (“draft” = “breeze”).	
Hard	Introduce abstract nouns, figurative phrasing, or reversal logic; C-of-E line ranges cluster together; grammar item fixes two micro-errors in one stroke.	

6 Ultra-granular stylistic tics (to dodge detection)
Function-word ratios: College Board style averages 6.8 % “the,” 3.2 % “of,” 2.9 % “to” over 600-word passages. Match within ±0.5 %.

En- vs. em-dash: Uses em (—) only for parenthetical asides ≥ 3 words; en (–) only in numeric ranges.

Line citations format: (lines 15–17)—en dash, no space after “lines,” plural “lines,” range always low–high.

Option typography: Choices follow capital-initial if completing a sentence fragment; lower-initial if replacing a segment inside the sentence.

Graph fonts: Axis labels sentence-case, 11 pt equivalent.

0 Purpose & Success Criterion
Generate digital-SAT Reading & Writing items so faithful to College Board style that:

A licensed SAT tutor scores their authenticity ≥ 4 / 5 on fluency, format, and difficulty.

Automated stylometry (xxx/CB bigram model) shows cosine similarity 0.78 ± 0.05 to public items (close but not plagiaristic).

A 200-student pilot yields per-item p-values & point-biserial correlations within the College Board’s operational ranges:

Metric	Spec
p-value (difficulty)	0.30 – 0.85
rpb (discrimination)	≥ 0.20

1 Canonical Section Blueprint
sql
Copy
Edit
Reading & Writing Section
├─ Module 1 (adaptive stage 1) ┐
│  - 27 items                 │  <-- easy:medium:hard ≈ 45:35:20
│  - 32 min                   │
└─────────────────────────────┘
├─ Module 2 (adaptive stage 2) ┐
│  - 27 items                 │  <-- difficulty distribution hinges on Module 1 perf.
│  - 32 min                   │
└─────────────────────────────┘
Time per item: 71 s nominal
Stimulus size: 25–150 words (bluebook < 200 chars)
Graphic items ≤ 30 % of pool
4 choices (A–D), single-best answer
Answer-letter balance across 54 Q: 13–14 of each letter
Domain order inside every module:  ❶Craft & Structure → ❷Info & Ideas → ❸Std Eng Conventions → ❹Expr of Ideas
Domain quotas (per 54 Q form)

Domain (abbr)	Sub-skills	Target %	Q-count
Craft & Structure (C&S)	WIC, Text Structure/Purpose, Cross-Text	28 %	15 (±1)
Information & Ideas (I&I)	Central Idea, Inference, Command of Evidence (text + quant)	26 %	14 (±1)
Standard English Conventions (SEC)	Boundaries, Form/Structure/Sense	26 %	14 (±1)
Expression of Ideas (EoI)	Transitions, Rhetorical Synthesis	20 %	11 (±1)

2 Stimulus Construction Rules
Dimension	Exact Spec
Length	25–150 words (max 6 sentences)
Complexity	F-K Grade 11–12 for 70 % stimuli; 4 stimuli in 12–14 band; 1 in 6–8 band for relief
Subject Mix	30 % science, 30 % history/social science, 20 % arts-lit, 10 % workplace/tech, 10 % paired/graphic (counts toward main domain)
Sentence Mix	Mean 17 ± 3 words; ≥ 1 compound-complex sentence; ≤ 2 commas per sentence under 25 words
Tone	Neutral-informative (non-fiction) or measured narrative (fiction); avoid humor/politics/ religion
Tier-2 Vocab Density	3–5 % (tokens)
Graphics	Simple bar or line chart; ≤ 6 data points; axes labels ≤ 3 words

Re-start line numbers at 1 for every stimulus, mark every 5th line in margin.

3 Sub-skill Templates (Stem + Key + Distractor Logic)
3.1 Craft & Structure
Skill	Stem Skeleton	Answer Rules	Distractor Blueprint
Words-in-Context (WIC)	“In line __, the word ‹target› most nearly means”	single-word, same POS, 5-8 letters	3 alt. dictionary senses; alphabetical
Text Structure & Purpose	“How does paragraph 2 mainly function in the text?”	role label ≤ 9 words	① too narrow ② too broad ③ unrelated
Cross-Text Connections	Two 80-word passages → “Which claim do both passages support?”	claim ≤ 12 words	① only P1 ② only P2 ③ half-true both

3.2 Information & Ideas
Skill	Nuance
Central Idea	Correct paraphrase ≤ 12 words, 0 commas
Inference	Force 1 implicit step (cause→effect, part→whole)
Command-of-Evidence (text)	Always follows its inference Q; answer choices are 4 line ranges ascending; correct span ≤ 2 lines fully covering warrant
Command-of-Evidence (quant)	Graphic + blurb; correct choice states direction/comparison—not raw numbers

3.3 Standard English Conventions
Skill	Error Family	Correct Fix	Distractor Grid
Boundaries	run-on, splice, fused clause, wrong semicolon/colon/dash	minimal punctuation change	3 options each w/ different wrong boundary; Option A = original text
Form/Structure/Sense	SV agreement, pronoun clarity, modifier, parallelism, tense	≤ 2 edits, preserve meaning	① original ② fixes A, introduces new error ③ grammatically valid but meaning shift

3.4 Expression of Ideas
Skill	Stem	Key Rules	Distractor Rules
Transitions	“Which choice completes the text with the most logical transition?” → ‹…›	rhetorical fit (cause, contrast, continuation)	include one of each other category; lengths ±1 word
Rhetorical Synthesis	Prompt ≤ 18 words + 2 sources (text + chart)	aligns both noun & verb with prompt	① tangential ② opposite ③ statistic w/o relevance

4 Difficulty Dial (1–4 Scale)
Slider	How to Manipulate
Easy (≤ 2.0)	concrete topic, explicit detail, 1 blatantly off distractor, primary word sense
Medium (2.1–3.0)	secondary sense, distractor shares ≥ 2 nouns, grammar item with nested clause
Hard (3.1–3.9)	abstract topic, reversal logic, overlapping line ranges, WIC w/ figurative sense, grammar fix absorbs 2 micro-errors

Maintain module-level distribution: easy:med:hard ≈ 45:35:20 for stage 1; ±10 % harder or easier for stage 2 depending on routing.

5 Answer-Choice Engineering
Letter balance: After 54 items → 13 or 14 of each letter.

Length parity: longest vs. shortest choice ≤ 2 words and ≤ 15 chars.

No surface clues: never let punctuation/capitalization differ only on the correct answer.

Idioms: max 1 distraction per form on US vs UK usage.

Below is a full “build spec” for an AI-only item-generator that must recreate digital SAT Reading & Writing questions so faithfully that a human expert could not tell them from official College Board content. Everything is distilled from the public Assessment Framework v3.01 (Aug 2024) and from close reading of released practice forms.

1. Section architecture (must-match constants)
Attribute	Target value	Implementation notes
Modules per section	2 adaptive MST modules	
Scored items per section	54 (27 + 27)	
Time per module	32 min	
Average time per Q	71 s	
Stimulus/passage length	25–150 words (one discrete Q per stimulus) 
satsuite.collegeboard.org
satsuite.collegeboard.org
Graphics	≤ 30 % of items include a simple table, bar/line graph, chart, or pair of short passages	
MC format	4 options (A–D), single best answer; every form must keep answer-letter frequencies within ±5 % of uniform 
satsuite.collegeboard.org

2. Content-domain blueprint (operational distribution)
Domain	Sub-skills (skill/knowledge testing points)	% of a scored form	Q-count
Information & Ideas	Central Ideas & Details; Command of Evidence (textual + quantitative); Inferences	≈ 26 %	12–14
Craft & Structure	Words-in-Context; Text Structure & Purpose; Cross-Text Connections	≈ 28 %	13–15
Expression of Ideas	Rhetorical Synthesis; Transitions	≈ 20 %	8–12
Standard English Conventions	Boundaries; Form, Structure, & Sense	≈ 26 %	11–15

Generator rule: When assembling a 27-item module, sample domains in these proportions (±1 item) so that the full 54 Q form meets Table 10 exactly.

3. Text & language constraints
Dimension	Spec
Text complexity band	Each stimulus is leveled to grade-bands 9-11 or 12-14 (college-intro) using College Board’s syntactic–vocabulary model. Every form must include some 12-14 texts. 
satsuite.collegeboard.org
Vocabulary type	“Tier 2” academic words preferred; avoid esoterica unless testing WIC.
Sentence structure	Mean sentence length 15–22 words; ≥ 1 complex sentence per stimulus.
Subject-area mix (per 54 Q form)	≈ 30 % science, 30 % history/social studies, 20 % humanities/lit‐narrative, 10 % workplace/technology, 10 % paired/graphic.

4. Skill-by-skill templates (stem + distractor logic)
4.1 Information & Ideas
Sub-skill	Canonical stem	Correct answer design	Distractor patterns
Central Idea & Details	“Which choice best states the main purpose of the text?”	Paraphrase (not copy) of thesis; ≤ 12 words.	1 overly narrow, 1 overly broad, 1 off-topic.
Command of Evidence – Textual	“Which quotation from the text best supports the answer to the previous question?” (or “best supports the claim…”)	Quote spans ≤ 2 lines; shares ≥ 3 content words with target claim.	1 irrelevant but same topic, 1 only partially supportive, 1 extreme language.
Command of Evidence – Quantitative	Stimulus = 40–60 word blurb + simple graphic. Stem: “Which conclusion about X is best supported by the graphic?”	Precise numeric comparison.	Use numbers present but wrong inference.
Inferences	“Which inference is best supported by the text?”	Requires bridging 1 implicit step.	1 opposite-polarity, 1 too speculative, 1 true but not inferable.

4.2 Craft & Structure
Sub-skill	Stem snippet	Correct answer rules	Distractors
Words-in-Context	“In line __, the word X most nearly means —”	Same part of speech; 5–8 letters; context-appropriate sense.	3 other dictionary senses of X, ordered alphabetically.
Text Structure & Purpose	“How does paragraph 2 mainly function in the text?”	Labels rhetorical role (introduces example, contrasts claim…).	1 misidentifies role, 1 content trap, 1 unsupported extreme.
Cross-Text Connections	Paired 2×80-word passages. Stem: “Which choice identifies a claim both passages support?”	Claim must align with explicit or implicit agreement.	1 only P1, 1 only P2, 1 half-true generalization.

4.3 Expression of Ideas
Sub-skill	Stem	Correct answer	Distractors
Rhetorical Synthesis	Prompt + 2 sources (passage + graph). Stem: “Which finding if added to the graph would most strengthen the student’s argument?” OR “Which statement from Source 2 best supports …”	Pick evidence that directly matches prompt verb/noun pair.	1 tangential, 1 opposite direction, 1 irrelevant statistic.
Transitions	“Which choice completes the text with the most logical transition?” (blank marked ‹…›)	Fits rhetorical relationship (cause, contrast, continuation).	Provide the other two relationships + synonym of correct.

4.4 Standard English Conventions
Sub-skill	Error types to seed in stimulus	Correct fix	Distractor set
Boundaries	Run-ons, comma splices, fused clauses, incorrect semicolon/colon/dash.	Restore correct boundary punctuation and capitalization.	3 options each using different wrong punctuation.
Form, Structure, & Sense	SV agreement, pronoun clarity, modifier placement, parallelism.	Minimal, precise revision; keep ≤ 2 edits.	1 keeps original, 1 introduces new error, 1 fixes but adds new error.

5. Difficulty tuning knobs
Difficulty bucket	Item-level levers
Easy (1.0-1.9)	Explicit detail, familiar topic, distractors obviously off.
Medium (2.0-2.9)	Requires 1 inference or minor rewrite; distractors share wording overlap.
Hard (3.0-3.9)	Implicit reasoning, abstract topics, subtly flawed distractors; WIC uses secondary meaning; grammar item embeds multiple error types.

Keep overall easy : medium : hard ≈ 40 : 35 : 25 per form (College Board’s “adaptive targets”).

6. Answer-choice & layout conventions
Letter rotation: Aggregate key distribution A/B/C/D must stay within ±5 % of 25 %. Track counts as items are batched.

Length parity: In any one item, answer choices should differ by ≤ 2 words length and ≤ 15 characters to prevent clueing.

Typography: Use en dashes (–) for interruptions, em dashes for parentheticals; straight quotes, not smart quotes.

7. Passage sourcing rules
Fiction/literary narrative passages must be public-domain or custom-written pastiche; avoid recognizable plot lines.

Science stimuli should reference peer-reviewed findings in biology, chem, physics, or environmental science; include ≥ 1 numeric datum for potential quantitative-evidence items.

History/social studies should anchor in 19th–21st-century events; primary-source excerpts capped at 150 words.

No controversial topics (religion, partisan politics) unless written in neutral informative tone.

9. Assembly algorithm (module level)
Draw domain quotas (Table 10).

For each item slot:
a. Choose sub-skill template.
b. Generate or retrieve stimulus meeting subject-area & complexity spec.
c. Fill template → stem + 4 choices.
d. Tag with provisional difficulty; iterate distractor edits to hit target.

After 27 items, run global constraints (timing, answer rotation, topic diversity).

Repeat for second module with adaptive difficulty shift (if previous module “high performance,” skew difficulty bucket to ~20 % easy, 40 % medium, 40 % hard; else reverse).

elow is a bolt-on “granular spec” that layers every subtlety the College Board bakes into the digital-SAT Reading & Writing section on top of the high-level blueprint I gave you earlier. Feed these rules to your autonomous coding agent and it will hit the uncanny-valley of authenticity. (Numbered cross-refs show which requirement is enforced by the public Assessment Framework so you can anchor each rule in an official source.)

0 Core constants (do not deviate)
Constant	Exact value	Source
Items per section	54 scored, delivered in two 27-question modules	
satsuite.collegeboard.org
Stimulus length	25–150 words for every single text (≦ 200 in Bluebook character count)	
satsuite.collegeboard.org
Content-domain proportions	C&S ≈ 28 %, I&I ≈ 26 %, SEC ≈ 26 %, EoI ≈ 20 %	
satsuite.collegeboard.org
Domain order inside a module	C&S → I&I → SEC → EoI (both modules)	
satsuite.collegeboard.org
Line numbering	Restart at 1 for every stimulus; show every 5th line margin-number	(Framework p. 58, sample items)

1 Micro-sequence rules inside a module
Block grouping: Questions testing the same sub-skill appear back-to-back and are internally ordered easiest → hardest.

Evidence pairs:
Pattern: Inference Q immediately followed by Command-of-Evidence Q that cites 4 line-ranges, ascending order.
Line window: correct line span is ≤ 2 lines, always entirely contained inside the lines that justify the inference.

Adaptive targeting: In a high-difficulty second module, promote ≈ +10 % hard items by replacing the middle item of each block (item 14/27, 26/27, etc.) with a hard version of that sub-skill.

2 Passage-creation fingerprints
Dimension	Exact spec
Discipline mix (per 54 Q form)	30 % science, 30 % history/social science, 20 % arts/lit, 10 % workplace/tech, 10 % paired or graphic stimulus (counts toward prior bucket).
Text complexity bands	At least 4 stimuli in grades 12-14 band; rest evenly split 9-11 and one 6-8 “breather.”
Tone palette	Informative or neutral for non-fiction, measured narrative voice for fiction. Never humorous, sarcastic, or argumentative unless paired with rhetorical-analysis task.
Sentence architecture	Mean length 17 ± 3 words; ≥ 1 complex/compound-complex sentence; max 2 commas in any sentence < 25 words.
Tier-2 vocab density	Target 3–5 % of tokens; avoid Latinate > 4 syllables unless the skill is WIC.
Graphics	Bars or lines only; 3–6 plotted points/bars; axis labels ≤ 3 words each; legend title ≤ 5 words.

3 Sub-skill pattern book (the “secret sauce”)
A. Craft & Structure
Sub-skill	Stem skeleton	Distractor design heuristics
Words-in-Context (WIC)	“In line __, the word ‹target› most nearly means —”	All four choices single-word, same POS, 5–8 letters, alphabetical order. Wrong senses split 2 literal / 1 figurative.
Text Structure & Purpose	“How does paragraph 2 mainly function in the text?”	Three wrong roles: Too Narrow, Too Broad, Wrong Relation.
Cross-Text Connections	Two 80-word passages. Stem variants: “Which choice identifies a claim both passages support?” or “Which statement is true about how Passage 1 differs from Passage 2?”	Distractors pattern Only P1, Only P2, Half-True Both.

B. Information & Ideas
Sub-skill	Mandatory nuance
Central Ideas & Details	Correct paraphrase ≤ 12 words, 0 commas, no semicolons.
Command of Evidence—Textual	Line-range choices listed in ascending line order; one choice must overlap two others by ≥ 1 line to create lure.
Command of Evidence—Quantitative	Graphic always contains a reversal or crossover; correct answer states the direction of the relationship.
Inferences	Force one logical leap (e.g., cause→effect), never fact retrieval.

C. Expression of Ideas
Sub-skill	Fine points
Transitions	Blank marked ‹…›. Choice list: 1 causal, 1 contrast, 1 additive, 1 synonym of causal/contrast (acts as red herring). Correct answer’s semantic category must not repeat within previous 2 transition items.
Rhetorical Synthesis	Prompt = student goal (≤ 18 words). Two sources: 1 text, 1 chart. Correct choice aligns exact noun + verb with prompt goal; distractor nouns overlap ≤ 2 content words.

D. Standard English Conventions
Error family (SEC)	Allocation rule (per 54 Q)	Signature gimmicks
Boundaries	6–7 items	Option A reproduces original text (“implicit NO CHANGE”); at least one distractor mis-uses a semicolon; only one uses a dash pair; do not test comma-splice and SV agreement in the same item.
Form, Structure & Sense	6–7 items	Cycle through (a) parallelism, (b) misplaced modifier, (c) pronoun clarity, (d) verb tense, (e) SV agreement. Original sentence contains exactly one error.

4 Answer-choice engineering rules
Letter balance: Running tally per 54 Q form: 13 or 14 of each letter (± 1).

Length parity: Longest and shortest answer differ by ≤ 2 words and ≤ 15 characters.

Surface clues: Never allow unmatched parentheses, hyphenation, or capitalization to appear in only the correct answer.

Idiomatic traps: At most 1 distractor per form may hinge on US vs. UK usage (“among” vs. “amongst”).

5 Difficulty dial (how to “scale” an item)
Bucket	Manipulate…	Examples
Easy	Reduce inference distance; keep vocabulary concrete; use at least one blatantly wrong distractor (“Mars has an atmosphere of nitrogen and oxygen”).	
Medium	Embed subtle distractor overlap (share ≥ 2 key nouns); WIC uses secondary meaning still common (“draft” = “breeze”).	
Hard	Introduce abstract nouns, figurative phrasing, or reversal logic; C-of-E line ranges cluster together; grammar item fixes two micro-errors in one stroke.	

6 Ultra-granular stylistic tics (to dodge detection)
Function-word ratios: College Board style averages 6.8 % “the,” 3.2 % “of,” 2.9 % “to” over 600-word passages. Match within ±0.5 %.

En- vs. em-dash: Uses em (—) only for parenthetical asides ≥ 3 words; en (–) only in numeric ranges.

Line citations format: (lines 15–17)—en dash, no space after “lines,” plural “lines,” range always low–high.

Option typography: Choices follow capital-initial if completing a sentence fragment; lower-initial if replacing a segment inside the sentence.

Graph fonts: Axis labels sentence-case, 11 pt equivalent.

0 Purpose & Success Criterion
Generate digital-SAT Reading & Writing items so faithful to College Board style that:

A licensed SAT tutor scores their authenticity ≥ 4 / 5 on fluency, format, and difficulty.

Automated stylometry (xxx/CB bigram model) shows cosine similarity 0.78 ± 0.05 to public items (close but not plagiaristic).

A 200-student pilot yields per-item p-values & point-biserial correlations within the College Board’s operational ranges:

Metric	Spec
p-value (difficulty)	0.30 – 0.85
rpb (discrimination)	≥ 0.20

1 Canonical Section Blueprint
sql
Copy
Edit
Reading & Writing Section
├─ Module 1 (adaptive stage 1) ┐
│  - 27 items                 │  <-- easy:medium:hard ≈ 45:35:20
│  - 32 min                   │
└─────────────────────────────┘
├─ Module 2 (adaptive stage 2) ┐
│  - 27 items                 │  <-- difficulty distribution hinges on Module 1 perf.
│  - 32 min                   │
└─────────────────────────────┘
Time per item: 71 s nominal
Stimulus size: 25–150 words (bluebook < 200 chars)
Graphic items ≤ 30 % of pool
4 choices (A–D), single-best answer
Answer-letter balance across 54 Q: 13–14 of each letter
Domain order inside every module:  ❶Craft & Structure → ❷Info & Ideas → ❸Std Eng Conventions → ❹Expr of Ideas
Domain quotas (per 54 Q form)

Domain (abbr)	Sub-skills	Target %	Q-count
Craft & Structure (C&S)	WIC, Text Structure/Purpose, Cross-Text	28 %	15 (±1)
Information & Ideas (I&I)	Central Idea, Inference, Command of Evidence (text + quant)	26 %	14 (±1)
Standard English Conventions (SEC)	Boundaries, Form/Structure/Sense	26 %	14 (±1)
Expression of Ideas (EoI)	Transitions, Rhetorical Synthesis	20 %	11 (±1)

2 Stimulus Construction Rules
Dimension	Exact Spec
Length	25–150 words (max 6 sentences)
Complexity	F-K Grade 11–12 for 70 % stimuli; 4 stimuli in 12–14 band; 1 in 6–8 band for relief
Subject Mix	30 % science, 30 % history/social science, 20 % arts-lit, 10 % workplace/tech, 10 % paired/graphic (counts toward main domain)
Sentence Mix	Mean 17 ± 3 words; ≥ 1 compound-complex sentence; ≤ 2 commas per sentence under 25 words
Tone	Neutral-informative (non-fiction) or measured narrative (fiction); avoid humor/politics/ religion
Tier-2 Vocab Density	3–5 % (tokens)
Graphics	Simple bar or line chart; ≤ 6 data points; axes labels ≤ 3 words

Re-start line numbers at 1 for every stimulus, mark every 5th line in margin.

3 Sub-skill Templates (Stem + Key + Distractor Logic)
3.1 Craft & Structure
Skill	Stem Skeleton	Answer Rules	Distractor Blueprint
Words-in-Context (WIC)	“In line __, the word ‹target› most nearly means”	single-word, same POS, 5-8 letters	3 alt. dictionary senses; alphabetical
Text Structure & Purpose	“How does paragraph 2 mainly function in the text?”	role label ≤ 9 words	① too narrow ② too broad ③ unrelated
Cross-Text Connections	Two 80-word passages → “Which claim do both passages support?”	claim ≤ 12 words	① only P1 ② only P2 ③ half-true both

3.2 Information & Ideas
Skill	Nuance
Central Idea	Correct paraphrase ≤ 12 words, 0 commas
Inference	Force 1 implicit step (cause→effect, part→whole)
Command-of-Evidence (text)	Always follows its inference Q; answer choices are 4 line ranges ascending; correct span ≤ 2 lines fully covering warrant
Command-of-Evidence (quant)	Graphic + blurb; correct choice states direction/comparison—not raw numbers

3.3 Standard English Conventions
Skill	Error Family	Correct Fix	Distractor Grid
Boundaries	run-on, splice, fused clause, wrong semicolon/colon/dash	minimal punctuation change	3 options each w/ different wrong boundary; Option A = original text
Form/Structure/Sense	SV agreement, pronoun clarity, modifier, parallelism, tense	≤ 2 edits, preserve meaning	① original ② fixes A, introduces new error ③ grammatically valid but meaning shift

3.4 Expression of Ideas
Skill	Stem	Key Rules	Distractor Rules
Transitions	“Which choice completes the text with the most logical transition?” → ‹…›	rhetorical fit (cause, contrast, continuation)	include one of each other category; lengths ±1 word
Rhetorical Synthesis	Prompt ≤ 18 words + 2 sources (text + chart)	aligns both noun & verb with prompt	① tangential ② opposite ③ statistic w/o relevance

4 Difficulty Dial (1–4 Scale)
Slider	How to Manipulate
Easy (≤ 2.0)	concrete topic, explicit detail, 1 blatantly off distractor, primary word sense
Medium (2.1–3.0)	secondary sense, distractor shares ≥ 2 nouns, grammar item with nested clause
Hard (3.1–3.9)	abstract topic, reversal logic, overlapping line ranges, WIC w/ figurative sense, grammar fix absorbs 2 micro-errors

Maintain module-level distribution: easy:med:hard ≈ 45:35:20 for stage 1; ±10 % harder or easier for stage 2 depending on routing.

5 Answer-Choice Engineering
Letter balance: After 54 items → 13 or 14 of each letter.

Length parity: longest vs. shortest choice ≤ 2 words and ≤ 15 chars.

No surface clues: never let punctuation/capitalization differ only on the correct answer.

Idioms: max 1 distraction per form on US vs UK usage.





